.add-credential-dialog {
  width: 100%;
  max-width: 650px;
  min-width: 320px;
  border-radius: 12px;
  overflow: hidden;
}

// Dialog title styling
h2[mat-dialog-title] {
  margin: 0;
  padding: 24px 24px 16px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 500;
  font-size: 24px;
  text-align: center;
}

.credential-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 24px 0;

  // Form field styling
  mat-form-field {
    .mat-mdc-form-field-subscript-wrapper {
      margin-top: 8px;
    }

    .mat-mdc-form-field-error {
      font-size: 12px;
      color: #f44336;
    }
  }
}

.full-width {
  width: 100%;
}

.form-row {
  display: flex;
  gap: 20px;
  align-items: flex-start;

  .half-width {
    flex: 1;
    min-width: 0; // Prevents flex items from overflowing
  }
}

.password-actions {
  display: flex;
  justify-content: flex-end;
  margin: 12px 0;

  button {
    display: flex;
    align-items: center;
    gap: 8px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    mat-icon {
      font-size: 20px;
      width: 20px;
      height: 20px;
    }
  }
}

.password-fields,
.api-key-fields {
  display: flex;
  flex-direction: column;
  gap: 20px;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

mat-dialog-content {
  max-height: 70vh;
  overflow-y: auto;
  padding: 0 32px;
  background: #fafafa;
}

mat-dialog-actions {
  padding: 20px 32px;
  gap: 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  margin-top: 0;
  background: white;

  button {
    min-width: 120px;
    height: 44px;
    border-radius: 8px;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    &.mat-mdc-raised-button {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }
}

// Responsive design improvements
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 20px;

    .half-width {
      width: 100%;
      flex: none;
    }
  }

  .add-credential-dialog {
    max-width: 95vw;
    margin: 12px;
    border-radius: 8px;
  }

  h2[mat-dialog-title] {
    padding: 20px 20px 12px 20px;
    font-size: 20px;
  }

  mat-dialog-content {
    padding: 0 20px;
    max-height: 65vh;
  }

  .credential-form {
    gap: 20px;
    padding: 20px 0;
  }

  mat-dialog-actions {
    padding: 16px 20px;
    flex-direction: column-reverse;
    gap: 12px;

    button {
      width: 100%;
      height: 48px;
      font-size: 16px;
    }
  }

  .password-actions {
    justify-content: center;

    button {
      width: 100%;
      justify-content: center;
      height: 44px;
    }
  }
}

@media (max-width: 480px) {
  .add-credential-dialog {
    max-width: 100vw;
    margin: 8px;
  }

  .credential-form {
    gap: 16px;
    padding: 16px 0;
  }

  mat-dialog-content {
    padding: 0 12px;
  }

  mat-dialog-actions {
    padding: 12px;
  }
}

// Form field improvements
mat-form-field {
  .mat-mdc-form-field-subscript-wrapper {
    margin-top: 4px;
  }
}

// Button styling improvements
.mat-mdc-raised-button {
  font-weight: 500;
  letter-spacing: 0.5px;
}

// Loading state
.mat-mdc-raised-button:disabled {
  opacity: 0.6;
}