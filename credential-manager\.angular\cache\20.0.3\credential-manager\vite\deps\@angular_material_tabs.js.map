{"version": 3, "sources": ["../../../../../../node_modules/@angular/material/fesm2022/tabs.mjs"], "sourcesContent": ["import { Focus<PERSON>eyManager, _IdGenerator, CdkMonitorFocus, FocusMonitor } from '@angular/cdk/a11y';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { hasModifierKey, SPACE, ENTER } from '@angular/cdk/keycodes';\nimport { SharedResizeObserver } from '@angular/cdk/observers/private';\nimport { Platform } from '@angular/cdk/platform';\nimport { ViewportRuler, CdkScrollable } from '@angular/cdk/scrolling';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, TemplateRef, Directive, ViewContainerRef, booleanAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ContentChild, ViewChild, ElementRef, ChangeDetectorRef, NgZone, Injector, Renderer2, EventEmitter, afterNextRender, numberAttribute, Output, ContentChildren, QueryList, ViewChildren, signal, forwardRef, computed, HostAttributeToken, NgModule } from '@angular/core';\nimport { Subject, of, merge, EMPTY, Observable, timer, Subscription, BehaviorSubject } from 'rxjs';\nimport { debounceTime, takeUntil, startWith, switchMap, skip, filter } from 'rxjs/operators';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { CdkPortal, TemplatePortal, CdkPortalOutlet } from '@angular/cdk/portal';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { _ as _StructuralStylesLoader } from './structural-styles-CObeNzjn.mjs';\nimport { CdkObserveContent } from '@angular/cdk/observers';\nimport { M as MatRipple, a as MAT_RIPPLE_GLOBAL_OPTIONS } from './ripple-BYgV4oZC.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport '@angular/cdk/layout';\nimport '@angular/cdk/coercion';\n\n/**\n * Injection token that can be used to reference instances of `MatTabContent`. It serves as\n * alternative token to the actual `MatTabContent` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst _c0 = [\"*\"];\nfunction MatTab_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nconst _c1 = [\"tabListContainer\"];\nconst _c2 = [\"tabList\"];\nconst _c3 = [\"tabListInner\"];\nconst _c4 = [\"nextPaginator\"];\nconst _c5 = [\"previousPaginator\"];\nconst _c6 = [\"content\"];\nfunction MatTabBody_ng_template_2_Template(rf, ctx) {}\nconst _c7 = [\"tabBodyWrapper\"];\nconst _c8 = [\"tabHeader\"];\nfunction MatTabGroup_For_3_Conditional_6_ng_template_0_Template(rf, ctx) {}\nfunction MatTabGroup_For_3_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MatTabGroup_For_3_Conditional_6_ng_template_0_Template, 0, 0, \"ng-template\", 12);\n  }\n  if (rf & 2) {\n    const tab_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"cdkPortalOutlet\", tab_r4.templateLabel);\n  }\n}\nfunction MatTabGroup_For_3_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const tab_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵtextInterpolate(tab_r4.textLabel);\n  }\n}\nfunction MatTabGroup_For_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7, 2);\n    i0.ɵɵlistener(\"click\", function MatTabGroup_For_3_Template_div_click_0_listener() {\n      const ctx_r2 = i0.ɵɵrestoreView(_r2);\n      const tab_r4 = ctx_r2.$implicit;\n      const $index_r5 = ctx_r2.$index;\n      const ctx_r5 = i0.ɵɵnextContext();\n      const tabHeader_r7 = i0.ɵɵreference(1);\n      return i0.ɵɵresetView(ctx_r5._handleClick(tab_r4, tabHeader_r7, $index_r5));\n    })(\"cdkFocusChange\", function MatTabGroup_For_3_Template_div_cdkFocusChange_0_listener($event) {\n      const $index_r5 = i0.ɵɵrestoreView(_r2).$index;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5._tabFocusChanged($event, $index_r5));\n    });\n    i0.ɵɵelement(2, \"span\", 8)(3, \"div\", 9);\n    i0.ɵɵelementStart(4, \"span\", 10)(5, \"span\", 11);\n    i0.ɵɵconditionalCreate(6, MatTabGroup_For_3_Conditional_6_Template, 1, 1, null, 12)(7, MatTabGroup_For_3_Conditional_7_Template, 1, 1);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const tab_r4 = ctx.$implicit;\n    const $index_r5 = ctx.$index;\n    const tabNode_r8 = i0.ɵɵreference(1);\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(tab_r4.labelClass);\n    i0.ɵɵclassProp(\"mdc-tab--active\", ctx_r5.selectedIndex === $index_r5);\n    i0.ɵɵproperty(\"id\", ctx_r5._getTabLabelId(tab_r4, $index_r5))(\"disabled\", tab_r4.disabled)(\"fitInkBarToContent\", ctx_r5.fitInkBarToContent);\n    i0.ɵɵattribute(\"tabIndex\", ctx_r5._getTabIndex($index_r5))(\"aria-posinset\", $index_r5 + 1)(\"aria-setsize\", ctx_r5._tabs.length)(\"aria-controls\", ctx_r5._getTabContentId($index_r5))(\"aria-selected\", ctx_r5.selectedIndex === $index_r5)(\"aria-label\", tab_r4.ariaLabel || null)(\"aria-labelledby\", !tab_r4.ariaLabel && tab_r4.ariaLabelledby ? tab_r4.ariaLabelledby : null);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"matRippleTrigger\", tabNode_r8)(\"matRippleDisabled\", tab_r4.disabled || ctx_r5.disableRipple);\n    i0.ɵɵadvance(3);\n    i0.ɵɵconditional(tab_r4.templateLabel ? 6 : 7);\n  }\n}\nfunction MatTabGroup_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nfunction MatTabGroup_For_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-tab-body\", 13);\n    i0.ɵɵlistener(\"_onCentered\", function MatTabGroup_For_8_Template_mat_tab_body__onCentered_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5._removeTabBodyWrapperHeight());\n    })(\"_onCentering\", function MatTabGroup_For_8_Template_mat_tab_body__onCentering_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5._setTabBodyWrapperHeight($event));\n    })(\"_beforeCentering\", function MatTabGroup_For_8_Template_mat_tab_body__beforeCentering_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5._bodyCentered($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r10 = ctx.$implicit;\n    const $index_r11 = ctx.$index;\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(tab_r10.bodyClass);\n    i0.ɵɵproperty(\"id\", ctx_r5._getTabContentId($index_r11))(\"content\", tab_r10.content)(\"position\", tab_r10.position)(\"animationDuration\", ctx_r5.animationDuration)(\"preserveContent\", ctx_r5.preserveContent);\n    i0.ɵɵattribute(\"tabindex\", ctx_r5.contentTabIndex != null && ctx_r5.selectedIndex === $index_r11 ? ctx_r5.contentTabIndex : null)(\"aria-labelledby\", ctx_r5._getTabLabelId(tab_r10, $index_r11))(\"aria-hidden\", ctx_r5.selectedIndex !== $index_r11);\n  }\n}\nconst _c9 = [\"mat-tab-nav-bar\", \"\"];\nconst _c10 = [\"mat-tab-link\", \"\"];\nconst MAT_TAB_CONTENT = new InjectionToken('MatTabContent');\n/** Decorates the `ng-template` tags and reads out the template from it. */\nclass MatTabContent {\n  template = inject(TemplateRef);\n  constructor() {}\n  static ɵfac = function MatTabContent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatTabContent)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatTabContent,\n    selectors: [[\"\", \"matTabContent\", \"\"]],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_TAB_CONTENT,\n      useExisting: MatTabContent\n    }])]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabContent, [{\n    type: Directive,\n    args: [{\n      selector: '[matTabContent]',\n      providers: [{\n        provide: MAT_TAB_CONTENT,\n        useExisting: MatTabContent\n      }]\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Injection token that can be used to reference instances of `MatTabLabel`. It serves as\n * alternative token to the actual `MatTabLabel` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_TAB_LABEL = new InjectionToken('MatTabLabel');\n/**\n * Used to provide a tab label to a tab without causing a circular dependency.\n * @docs-private\n */\nconst MAT_TAB = new InjectionToken('MAT_TAB');\n/** Used to flag tab labels for use with the portal directive */\nclass MatTabLabel extends CdkPortal {\n  _closestTab = inject(MAT_TAB, {\n    optional: true\n  });\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatTabLabel_BaseFactory;\n    return function MatTabLabel_Factory(__ngFactoryType__) {\n      return (ɵMatTabLabel_BaseFactory || (ɵMatTabLabel_BaseFactory = i0.ɵɵgetInheritedFactory(MatTabLabel)))(__ngFactoryType__ || MatTabLabel);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatTabLabel,\n    selectors: [[\"\", \"mat-tab-label\", \"\"], [\"\", \"matTabLabel\", \"\"]],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_TAB_LABEL,\n      useExisting: MatTabLabel\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabLabel, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-tab-label], [matTabLabel]',\n      providers: [{\n        provide: MAT_TAB_LABEL,\n        useExisting: MatTabLabel\n      }]\n    }]\n  }], null, null);\n})();\n\n/**\n * Used to provide a tab group to a tab without causing a circular dependency.\n * @docs-private\n */\nconst MAT_TAB_GROUP = new InjectionToken('MAT_TAB_GROUP');\nclass MatTab {\n  _viewContainerRef = inject(ViewContainerRef);\n  _closestTabGroup = inject(MAT_TAB_GROUP, {\n    optional: true\n  });\n  /** whether the tab is disabled. */\n  disabled = false;\n  /** Content for the tab label given by `<ng-template mat-tab-label>`. */\n  get templateLabel() {\n    return this._templateLabel;\n  }\n  set templateLabel(value) {\n    this._setTemplateLabelInput(value);\n  }\n  _templateLabel;\n  /**\n   * Template provided in the tab content that will be used if present, used to enable lazy-loading\n   */\n  _explicitContent = undefined;\n  /** Template inside the MatTab view that contains an `<ng-content>`. */\n  _implicitContent;\n  /** Plain text label for the tab, used when there is no template label. */\n  textLabel = '';\n  /** Aria label for the tab. */\n  ariaLabel;\n  /**\n   * Reference to the element that the tab is labelled by.\n   * Will be cleared if `aria-label` is set at the same time.\n   */\n  ariaLabelledby;\n  /** Classes to be passed to the tab label inside the mat-tab-header container. */\n  labelClass;\n  /** Classes to be passed to the tab mat-tab-body container. */\n  bodyClass;\n  /**\n   * Custom ID for the tab, overriding the auto-generated one by Material.\n   * Note that when using this input, it's your responsibility to ensure that the ID is unique.\n   */\n  id = null;\n  /** Portal that will be the hosted content of the tab */\n  _contentPortal = null;\n  /** @docs-private */\n  get content() {\n    return this._contentPortal;\n  }\n  /** Emits whenever the internal state of the tab changes. */\n  _stateChanges = new Subject();\n  /**\n   * The relatively indexed position where 0 represents the center, negative is left, and positive\n   * represents the right.\n   */\n  position = null;\n  // TODO(crisbeto): we no longer use this, but some internal apps appear to rely on it.\n  /**\n   * The initial relatively index origin of the tab if it was created and selected after there\n   * was already a selected tab. Provides context of what position the tab should originate from.\n   */\n  origin = null;\n  /**\n   * Whether the tab is currently active.\n   */\n  isActive = false;\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n  }\n  ngOnChanges(changes) {\n    if (changes.hasOwnProperty('textLabel') || changes.hasOwnProperty('disabled')) {\n      this._stateChanges.next();\n    }\n  }\n  ngOnDestroy() {\n    this._stateChanges.complete();\n  }\n  ngOnInit() {\n    this._contentPortal = new TemplatePortal(this._explicitContent || this._implicitContent, this._viewContainerRef);\n  }\n  /**\n   * This has been extracted to a util because of TS 4 and VE.\n   * View Engine doesn't support property rename inheritance.\n   * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n   * @docs-private\n   */\n  _setTemplateLabelInput(value) {\n    // Only update the label if the query managed to find one. This works around an issue where a\n    // user may have manually set `templateLabel` during creation mode, which would then get\n    // clobbered by `undefined` when the query resolves. Also note that we check that the closest\n    // tab matches the current one so that we don't pick up labels from nested tabs.\n    if (value && value._closestTab === this) {\n      this._templateLabel = value;\n    }\n  }\n  static ɵfac = function MatTab_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatTab)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatTab,\n    selectors: [[\"mat-tab\"]],\n    contentQueries: function MatTab_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatTabLabel, 5);\n        i0.ɵɵcontentQuery(dirIndex, MatTabContent, 7, TemplateRef);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templateLabel = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._explicitContent = _t.first);\n      }\n    },\n    viewQuery: function MatTab_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(TemplateRef, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._implicitContent = _t.first);\n      }\n    },\n    hostAttrs: [\"hidden\", \"\"],\n    hostVars: 1,\n    hostBindings: function MatTab_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"id\", null);\n      }\n    },\n    inputs: {\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n      textLabel: [0, \"label\", \"textLabel\"],\n      ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n      ariaLabelledby: [0, \"aria-labelledby\", \"ariaLabelledby\"],\n      labelClass: \"labelClass\",\n      bodyClass: \"bodyClass\",\n      id: \"id\"\n    },\n    exportAs: [\"matTab\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_TAB,\n      useExisting: MatTab\n    }]), i0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function MatTab_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵtemplate(0, MatTab_ng_template_0_Template, 1, 0, \"ng-template\");\n      }\n    },\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTab, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tab',\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      exportAs: 'matTab',\n      providers: [{\n        provide: MAT_TAB,\n        useExisting: MatTab\n      }],\n      host: {\n        // This element will be rendered on the server in order to support hydration.\n        // Hide it so it doesn't cause a layout shift when it's removed on the client.\n        'hidden': '',\n        // Clear any custom IDs from the tab since they'll be forwarded to the actual tab.\n        '[attr.id]': 'null'\n      },\n      template: \"<!-- Create a template for the content of the <mat-tab> so that we can grab a reference to this\\n    TemplateRef and use it in a Portal to render the tab content in the appropriate place in the\\n    tab-group. -->\\n<ng-template><ng-content></ng-content></ng-template>\\n\"\n    }]\n  }], () => [], {\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    templateLabel: [{\n      type: ContentChild,\n      args: [MatTabLabel]\n    }],\n    _explicitContent: [{\n      type: ContentChild,\n      args: [MatTabContent, {\n        read: TemplateRef,\n        static: true\n      }]\n    }],\n    _implicitContent: [{\n      type: ViewChild,\n      args: [TemplateRef, {\n        static: true\n      }]\n    }],\n    textLabel: [{\n      type: Input,\n      args: ['label']\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    labelClass: [{\n      type: Input\n    }],\n    bodyClass: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }]\n  });\n})();\n\n/** Class that is applied when a tab indicator is active. */\nconst ACTIVE_CLASS = 'mdc-tab-indicator--active';\n/** Class that is applied when the tab indicator should not transition. */\nconst NO_TRANSITION_CLASS = 'mdc-tab-indicator--no-transition';\n/**\n * Abstraction around the MDC tab indicator that acts as the tab header's ink bar.\n * @docs-private\n */\nclass MatInkBar {\n  _items;\n  /** Item to which the ink bar is aligned currently. */\n  _currentItem;\n  constructor(_items) {\n    this._items = _items;\n  }\n  /** Hides the ink bar. */\n  hide() {\n    this._items.forEach(item => item.deactivateInkBar());\n    this._currentItem = undefined;\n  }\n  /** Aligns the ink bar to a DOM node. */\n  alignToElement(element) {\n    const correspondingItem = this._items.find(item => item.elementRef.nativeElement === element);\n    const currentItem = this._currentItem;\n    if (correspondingItem === currentItem) {\n      return;\n    }\n    currentItem?.deactivateInkBar();\n    if (correspondingItem) {\n      const domRect = currentItem?.elementRef.nativeElement.getBoundingClientRect?.();\n      // The ink bar won't animate unless we give it the `DOMRect` of the previous item.\n      correspondingItem.activateInkBar(domRect);\n      this._currentItem = correspondingItem;\n    }\n  }\n}\nclass InkBarItem {\n  _elementRef = inject(ElementRef);\n  _inkBarElement;\n  _inkBarContentElement;\n  _fitToContent = false;\n  /** Whether the ink bar should fit to the entire tab or just its content. */\n  get fitInkBarToContent() {\n    return this._fitToContent;\n  }\n  set fitInkBarToContent(newValue) {\n    if (this._fitToContent !== newValue) {\n      this._fitToContent = newValue;\n      if (this._inkBarElement) {\n        this._appendInkBarElement();\n      }\n    }\n  }\n  /** Aligns the ink bar to the current item. */\n  activateInkBar(previousIndicatorClientRect) {\n    const element = this._elementRef.nativeElement;\n    // Early exit if no indicator is present to handle cases where an indicator\n    // may be activated without a prior indicator state\n    if (!previousIndicatorClientRect || !element.getBoundingClientRect || !this._inkBarContentElement) {\n      element.classList.add(ACTIVE_CLASS);\n      return;\n    }\n    // This animation uses the FLIP approach. You can read more about it at the link below:\n    // https://aerotwist.com/blog/flip-your-animations/\n    // Calculate the dimensions based on the dimensions of the previous indicator\n    const currentClientRect = element.getBoundingClientRect();\n    const widthDelta = previousIndicatorClientRect.width / currentClientRect.width;\n    const xPosition = previousIndicatorClientRect.left - currentClientRect.left;\n    element.classList.add(NO_TRANSITION_CLASS);\n    this._inkBarContentElement.style.setProperty('transform', `translateX(${xPosition}px) scaleX(${widthDelta})`);\n    // Force repaint before updating classes and transform to ensure the transform properly takes effect\n    element.getBoundingClientRect();\n    element.classList.remove(NO_TRANSITION_CLASS);\n    element.classList.add(ACTIVE_CLASS);\n    this._inkBarContentElement.style.setProperty('transform', '');\n  }\n  /** Removes the ink bar from the current item. */\n  deactivateInkBar() {\n    this._elementRef.nativeElement.classList.remove(ACTIVE_CLASS);\n  }\n  /** Initializes the foundation. */\n  ngOnInit() {\n    this._createInkBarElement();\n  }\n  /** Destroys the foundation. */\n  ngOnDestroy() {\n    this._inkBarElement?.remove();\n    this._inkBarElement = this._inkBarContentElement = null;\n  }\n  /** Creates and appends the ink bar element. */\n  _createInkBarElement() {\n    const documentNode = this._elementRef.nativeElement.ownerDocument || document;\n    const inkBarElement = this._inkBarElement = documentNode.createElement('span');\n    const inkBarContentElement = this._inkBarContentElement = documentNode.createElement('span');\n    inkBarElement.className = 'mdc-tab-indicator';\n    inkBarContentElement.className = 'mdc-tab-indicator__content mdc-tab-indicator__content--underline';\n    inkBarElement.appendChild(this._inkBarContentElement);\n    this._appendInkBarElement();\n  }\n  /**\n   * Appends the ink bar to the tab host element or content, depending on whether\n   * the ink bar should fit to content.\n   */\n  _appendInkBarElement() {\n    if (!this._inkBarElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('Ink bar element has not been created and cannot be appended');\n    }\n    const parentElement = this._fitToContent ? this._elementRef.nativeElement.querySelector('.mdc-tab__content') : this._elementRef.nativeElement;\n    if (!parentElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('Missing element to host the ink bar');\n    }\n    parentElement.appendChild(this._inkBarElement);\n  }\n  static ɵfac = function InkBarItem_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || InkBarItem)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: InkBarItem,\n    inputs: {\n      fitInkBarToContent: [2, \"fitInkBarToContent\", \"fitInkBarToContent\", booleanAttribute]\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InkBarItem, [{\n    type: Directive\n  }], null, {\n    fitInkBarToContent: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n/**\n * The default positioner function for the MatInkBar.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction _MAT_INK_BAR_POSITIONER_FACTORY() {\n  const method = element => ({\n    left: element ? (element.offsetLeft || 0) + 'px' : '0',\n    width: element ? (element.offsetWidth || 0) + 'px' : '0'\n  });\n  return method;\n}\n/** Injection token for the MatInkBar's Positioner. */\nconst _MAT_INK_BAR_POSITIONER = new InjectionToken('MatInkBarPositioner', {\n  providedIn: 'root',\n  factory: _MAT_INK_BAR_POSITIONER_FACTORY\n});\n\n/**\n * Used in the `mat-tab-group` view to display tab labels.\n * @docs-private\n */\nclass MatTabLabelWrapper extends InkBarItem {\n  elementRef = inject(ElementRef);\n  /** Whether the tab is disabled. */\n  disabled = false;\n  /** Sets focus on the wrapper element */\n  focus() {\n    this.elementRef.nativeElement.focus();\n  }\n  getOffsetLeft() {\n    return this.elementRef.nativeElement.offsetLeft;\n  }\n  getOffsetWidth() {\n    return this.elementRef.nativeElement.offsetWidth;\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatTabLabelWrapper_BaseFactory;\n    return function MatTabLabelWrapper_Factory(__ngFactoryType__) {\n      return (ɵMatTabLabelWrapper_BaseFactory || (ɵMatTabLabelWrapper_BaseFactory = i0.ɵɵgetInheritedFactory(MatTabLabelWrapper)))(__ngFactoryType__ || MatTabLabelWrapper);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatTabLabelWrapper,\n    selectors: [[\"\", \"matTabLabelWrapper\", \"\"]],\n    hostVars: 3,\n    hostBindings: function MatTabLabelWrapper_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-disabled\", !!ctx.disabled);\n        i0.ɵɵclassProp(\"mat-mdc-tab-disabled\", ctx.disabled);\n      }\n    },\n    inputs: {\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute]\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabLabelWrapper, [{\n    type: Directive,\n    args: [{\n      selector: '[matTabLabelWrapper]',\n      host: {\n        '[class.mat-mdc-tab-disabled]': 'disabled',\n        '[attr.aria-disabled]': '!!disabled'\n      }\n    }]\n  }], null, {\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/** Config used to bind passive event listeners */\nconst passiveEventListenerOptions = {\n  passive: true\n};\n/**\n * Amount of milliseconds to wait before starting to scroll the header automatically.\n * Set a little conservatively in order to handle fake events dispatched on touch devices.\n */\nconst HEADER_SCROLL_DELAY = 650;\n/**\n * Interval in milliseconds at which to scroll the header\n * while the user is holding their pointer.\n */\nconst HEADER_SCROLL_INTERVAL = 100;\n/**\n * Base class for a tab header that supported pagination.\n * @docs-private\n */\nclass MatPaginatedTabHeader {\n  _elementRef = inject(ElementRef);\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _viewportRuler = inject(ViewportRuler);\n  _dir = inject(Directionality, {\n    optional: true\n  });\n  _ngZone = inject(NgZone);\n  _platform = inject(Platform);\n  _sharedResizeObserver = inject(SharedResizeObserver);\n  _injector = inject(Injector);\n  _renderer = inject(Renderer2);\n  _animationsDisabled = _animationsDisabled();\n  _eventCleanups;\n  /** The distance in pixels that the tab labels should be translated to the left. */\n  _scrollDistance = 0;\n  /** Whether the header should scroll to the selected index after the view has been checked. */\n  _selectedIndexChanged = false;\n  /** Emits when the component is destroyed. */\n  _destroyed = new Subject();\n  /** Whether the controls for pagination should be displayed */\n  _showPaginationControls = false;\n  /** Whether the tab list can be scrolled more towards the end of the tab label list. */\n  _disableScrollAfter = true;\n  /** Whether the tab list can be scrolled more towards the beginning of the tab label list. */\n  _disableScrollBefore = true;\n  /**\n   * The number of tab labels that are displayed on the header. When this changes, the header\n   * should re-evaluate the scroll position.\n   */\n  _tabLabelCount;\n  /** Whether the scroll distance has changed and should be applied after the view is checked. */\n  _scrollDistanceChanged;\n  /** Used to manage focus between the tabs. */\n  _keyManager;\n  /** Cached text content of the header. */\n  _currentTextContent;\n  /** Stream that will stop the automated scrolling. */\n  _stopScrolling = new Subject();\n  /**\n   * Whether pagination should be disabled. This can be used to avoid unnecessary\n   * layout recalculations if it's known that pagination won't be required.\n   */\n  disablePagination = false;\n  /** The index of the active tab. */\n  get selectedIndex() {\n    return this._selectedIndex;\n  }\n  set selectedIndex(v) {\n    const value = isNaN(v) ? 0 : v;\n    if (this._selectedIndex != value) {\n      this._selectedIndexChanged = true;\n      this._selectedIndex = value;\n      if (this._keyManager) {\n        this._keyManager.updateActiveItem(value);\n      }\n    }\n  }\n  _selectedIndex = 0;\n  /** Event emitted when the option is selected. */\n  selectFocusedIndex = new EventEmitter();\n  /** Event emitted when a label is focused. */\n  indexFocused = new EventEmitter();\n  constructor() {\n    // Bind the `mouseleave` event on the outside since it doesn't change anything in the view.\n    this._eventCleanups = this._ngZone.runOutsideAngular(() => [this._renderer.listen(this._elementRef.nativeElement, 'mouseleave', () => this._stopInterval())]);\n  }\n  ngAfterViewInit() {\n    // We need to handle these events manually, because we want to bind passive event listeners.\n    this._eventCleanups.push(this._renderer.listen(this._previousPaginator.nativeElement, 'touchstart', () => this._handlePaginatorPress('before'), passiveEventListenerOptions), this._renderer.listen(this._nextPaginator.nativeElement, 'touchstart', () => this._handlePaginatorPress('after'), passiveEventListenerOptions));\n  }\n  ngAfterContentInit() {\n    const dirChange = this._dir ? this._dir.change : of('ltr');\n    // We need to debounce resize events because the alignment logic is expensive.\n    // If someone animates the width of tabs, we don't want to realign on every animation frame.\n    // Once we haven't seen any more resize events in the last 32ms (~2 animaion frames) we can\n    // re-align.\n    const resize = this._sharedResizeObserver.observe(this._elementRef.nativeElement).pipe(debounceTime(32), takeUntil(this._destroyed));\n    // Note: We do not actually need to watch these events for proper functioning of the tabs,\n    // the resize events above should capture any viewport resize that we care about. However,\n    // removing this is fairly breaking for screenshot tests, so we're leaving it here for now.\n    const viewportResize = this._viewportRuler.change(150).pipe(takeUntil(this._destroyed));\n    const realign = () => {\n      this.updatePagination();\n      this._alignInkBarToSelectedTab();\n    };\n    this._keyManager = new FocusKeyManager(this._items).withHorizontalOrientation(this._getLayoutDirection()).withHomeAndEnd().withWrap()\n    // Allow focus to land on disabled tabs, as per https://w3c.github.io/aria-practices/#kbd_disabled_controls\n    .skipPredicate(() => false);\n    // Fall back to the first link as being active if there isn't a selected one.\n    // This is relevant primarily for the tab nav bar.\n    this._keyManager.updateActiveItem(Math.max(this._selectedIndex, 0));\n    // Note: We do not need to realign after the first render for proper functioning of the tabs\n    // the resize events above should fire when we first start observing the element. However,\n    // removing this is fairly breaking for screenshot tests, so we're leaving it here for now.\n    afterNextRender(realign, {\n      injector: this._injector\n    });\n    // On dir change or resize, realign the ink bar and update the orientation of\n    // the key manager if the direction has changed.\n    merge(dirChange, viewportResize, resize, this._items.changes, this._itemsResized()).pipe(takeUntil(this._destroyed)).subscribe(() => {\n      // We need to defer this to give the browser some time to recalculate\n      // the element dimensions. The call has to be wrapped in `NgZone.run`,\n      // because the viewport change handler runs outside of Angular.\n      this._ngZone.run(() => {\n        Promise.resolve().then(() => {\n          // Clamp the scroll distance, because it can change with the number of tabs.\n          this._scrollDistance = Math.max(0, Math.min(this._getMaxScrollDistance(), this._scrollDistance));\n          realign();\n        });\n      });\n      this._keyManager?.withHorizontalOrientation(this._getLayoutDirection());\n    });\n    // If there is a change in the focus key manager we need to emit the `indexFocused`\n    // event in order to provide a public event that notifies about focus changes. Also we realign\n    // the tabs container by scrolling the new focused tab into the visible section.\n    this._keyManager.change.subscribe(newFocusIndex => {\n      this.indexFocused.emit(newFocusIndex);\n      this._setTabFocus(newFocusIndex);\n    });\n  }\n  /** Sends any changes that could affect the layout of the items. */\n  _itemsResized() {\n    if (typeof ResizeObserver !== 'function') {\n      return EMPTY;\n    }\n    return this._items.changes.pipe(startWith(this._items), switchMap(tabItems => new Observable(observer => this._ngZone.runOutsideAngular(() => {\n      const resizeObserver = new ResizeObserver(entries => observer.next(entries));\n      tabItems.forEach(item => resizeObserver.observe(item.elementRef.nativeElement));\n      return () => {\n        resizeObserver.disconnect();\n      };\n    }))),\n    // Skip the first emit since the resize observer emits when an item\n    // is observed for new items when the tab is already inserted\n    skip(1),\n    // Skip emissions where all the elements are invisible since we don't want\n    // the header to try and re-render with invalid measurements. See #25574.\n    filter(entries => entries.some(e => e.contentRect.width > 0 && e.contentRect.height > 0)));\n  }\n  ngAfterContentChecked() {\n    // If the number of tab labels have changed, check if scrolling should be enabled\n    if (this._tabLabelCount != this._items.length) {\n      this.updatePagination();\n      this._tabLabelCount = this._items.length;\n      this._changeDetectorRef.markForCheck();\n    }\n    // If the selected index has changed, scroll to the label and check if the scrolling controls\n    // should be disabled.\n    if (this._selectedIndexChanged) {\n      this._scrollToLabel(this._selectedIndex);\n      this._checkScrollingControls();\n      this._alignInkBarToSelectedTab();\n      this._selectedIndexChanged = false;\n      this._changeDetectorRef.markForCheck();\n    }\n    // If the scroll distance has been changed (tab selected, focused, scroll controls activated),\n    // then translate the header to reflect this.\n    if (this._scrollDistanceChanged) {\n      this._updateTabScrollPosition();\n      this._scrollDistanceChanged = false;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  ngOnDestroy() {\n    this._eventCleanups.forEach(cleanup => cleanup());\n    this._keyManager?.destroy();\n    this._destroyed.next();\n    this._destroyed.complete();\n    this._stopScrolling.complete();\n  }\n  /** Handles keyboard events on the header. */\n  _handleKeydown(event) {\n    // We don't handle any key bindings with a modifier key.\n    if (hasModifierKey(event)) {\n      return;\n    }\n    switch (event.keyCode) {\n      case ENTER:\n      case SPACE:\n        if (this.focusIndex !== this.selectedIndex) {\n          const item = this._items.get(this.focusIndex);\n          if (item && !item.disabled) {\n            this.selectFocusedIndex.emit(this.focusIndex);\n            this._itemSelected(event);\n          }\n        }\n        break;\n      default:\n        this._keyManager?.onKeydown(event);\n    }\n  }\n  /**\n   * Callback for when the MutationObserver detects that the content has changed.\n   */\n  _onContentChanges() {\n    const textContent = this._elementRef.nativeElement.textContent;\n    // We need to diff the text content of the header, because the MutationObserver callback\n    // will fire even if the text content didn't change which is inefficient and is prone\n    // to infinite loops if a poorly constructed expression is passed in (see #14249).\n    if (textContent !== this._currentTextContent) {\n      this._currentTextContent = textContent || '';\n      // The content observer runs outside the `NgZone` by default, which\n      // means that we need to bring the callback back in ourselves.\n      this._ngZone.run(() => {\n        this.updatePagination();\n        this._alignInkBarToSelectedTab();\n        this._changeDetectorRef.markForCheck();\n      });\n    }\n  }\n  /**\n   * Updates the view whether pagination should be enabled or not.\n   *\n   * WARNING: Calling this method can be very costly in terms of performance. It should be called\n   * as infrequently as possible from outside of the Tabs component as it causes a reflow of the\n   * page.\n   */\n  updatePagination() {\n    this._checkPaginationEnabled();\n    this._checkScrollingControls();\n    this._updateTabScrollPosition();\n  }\n  /** Tracks which element has focus; used for keyboard navigation */\n  get focusIndex() {\n    return this._keyManager ? this._keyManager.activeItemIndex : 0;\n  }\n  /** When the focus index is set, we must manually send focus to the correct label */\n  set focusIndex(value) {\n    if (!this._isValidIndex(value) || this.focusIndex === value || !this._keyManager) {\n      return;\n    }\n    this._keyManager.setActiveItem(value);\n  }\n  /**\n   * Determines if an index is valid.  If the tabs are not ready yet, we assume that the user is\n   * providing a valid index and return true.\n   */\n  _isValidIndex(index) {\n    return this._items ? !!this._items.toArray()[index] : true;\n  }\n  /**\n   * Sets focus on the HTML element for the label wrapper and scrolls it into the view if\n   * scrolling is enabled.\n   */\n  _setTabFocus(tabIndex) {\n    if (this._showPaginationControls) {\n      this._scrollToLabel(tabIndex);\n    }\n    if (this._items && this._items.length) {\n      this._items.toArray()[tabIndex].focus();\n      // Do not let the browser manage scrolling to focus the element, this will be handled\n      // by using translation. In LTR, the scroll left should be 0. In RTL, the scroll width\n      // should be the full width minus the offset width.\n      const containerEl = this._tabListContainer.nativeElement;\n      const dir = this._getLayoutDirection();\n      if (dir == 'ltr') {\n        containerEl.scrollLeft = 0;\n      } else {\n        containerEl.scrollLeft = containerEl.scrollWidth - containerEl.offsetWidth;\n      }\n    }\n  }\n  /** The layout direction of the containing app. */\n  _getLayoutDirection() {\n    return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n  }\n  /** Performs the CSS transformation on the tab list that will cause the list to scroll. */\n  _updateTabScrollPosition() {\n    if (this.disablePagination) {\n      return;\n    }\n    const scrollDistance = this.scrollDistance;\n    const translateX = this._getLayoutDirection() === 'ltr' ? -scrollDistance : scrollDistance;\n    // Don't use `translate3d` here because we don't want to create a new layer. A new layer\n    // seems to cause flickering and overflow in Internet Explorer. For example, the ink bar\n    // and ripples will exceed the boundaries of the visible tab bar.\n    // See: https://github.com/angular/components/issues/10276\n    // We round the `transform` here, because transforms with sub-pixel precision cause some\n    // browsers to blur the content of the element.\n    this._tabList.nativeElement.style.transform = `translateX(${Math.round(translateX)}px)`;\n    // Setting the `transform` on IE will change the scroll offset of the parent, causing the\n    // position to be thrown off in some cases. We have to reset it ourselves to ensure that\n    // it doesn't get thrown off. Note that we scope it only to IE and Edge, because messing\n    // with the scroll position throws off Chrome 71+ in RTL mode (see #14689).\n    if (this._platform.TRIDENT || this._platform.EDGE) {\n      this._tabListContainer.nativeElement.scrollLeft = 0;\n    }\n  }\n  /** Sets the distance in pixels that the tab header should be transformed in the X-axis. */\n  get scrollDistance() {\n    return this._scrollDistance;\n  }\n  set scrollDistance(value) {\n    this._scrollTo(value);\n  }\n  /**\n   * Moves the tab list in the 'before' or 'after' direction (towards the beginning of the list or\n   * the end of the list, respectively). The distance to scroll is computed to be a third of the\n   * length of the tab list view window.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n  _scrollHeader(direction) {\n    const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n    // Move the scroll distance one-third the length of the tab list's viewport.\n    const scrollAmount = (direction == 'before' ? -1 : 1) * viewLength / 3;\n    return this._scrollTo(this._scrollDistance + scrollAmount);\n  }\n  /** Handles click events on the pagination arrows. */\n  _handlePaginatorClick(direction) {\n    this._stopInterval();\n    this._scrollHeader(direction);\n  }\n  /**\n   * Moves the tab list such that the desired tab label (marked by index) is moved into view.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n  _scrollToLabel(labelIndex) {\n    if (this.disablePagination) {\n      return;\n    }\n    const selectedLabel = this._items ? this._items.toArray()[labelIndex] : null;\n    if (!selectedLabel) {\n      return;\n    }\n    // The view length is the visible width of the tab labels.\n    const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n    const {\n      offsetLeft,\n      offsetWidth\n    } = selectedLabel.elementRef.nativeElement;\n    let labelBeforePos, labelAfterPos;\n    if (this._getLayoutDirection() == 'ltr') {\n      labelBeforePos = offsetLeft;\n      labelAfterPos = labelBeforePos + offsetWidth;\n    } else {\n      labelAfterPos = this._tabListInner.nativeElement.offsetWidth - offsetLeft;\n      labelBeforePos = labelAfterPos - offsetWidth;\n    }\n    const beforeVisiblePos = this.scrollDistance;\n    const afterVisiblePos = this.scrollDistance + viewLength;\n    if (labelBeforePos < beforeVisiblePos) {\n      // Scroll header to move label to the before direction\n      this.scrollDistance -= beforeVisiblePos - labelBeforePos;\n    } else if (labelAfterPos > afterVisiblePos) {\n      // Scroll header to move label to the after direction\n      this.scrollDistance += Math.min(labelAfterPos - afterVisiblePos, labelBeforePos - beforeVisiblePos);\n    }\n  }\n  /**\n   * Evaluate whether the pagination controls should be displayed. If the scroll width of the\n   * tab list is wider than the size of the header container, then the pagination controls should\n   * be shown.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n  _checkPaginationEnabled() {\n    if (this.disablePagination) {\n      this._showPaginationControls = false;\n    } else {\n      const scrollWidth = this._tabListInner.nativeElement.scrollWidth;\n      const containerWidth = this._elementRef.nativeElement.offsetWidth;\n      // Usually checking that the scroll width is greater than the container width should be\n      // enough, but on Safari at specific widths the browser ends up rounding up when there's\n      // no pagination and rounding down once the pagination is added. This can throw the component\n      // into an infinite loop where the pagination shows up and disappears constantly. We work\n      // around it by adding a threshold to the calculation. From manual testing the threshold\n      // can be lowered to 2px and still resolve the issue, but we set a higher one to be safe.\n      // This shouldn't cause any content to be clipped, because tabs have a 24px horizontal\n      // padding. See b/316395154 for more information.\n      const isEnabled = scrollWidth - containerWidth >= 5;\n      if (!isEnabled) {\n        this.scrollDistance = 0;\n      }\n      if (isEnabled !== this._showPaginationControls) {\n        this._showPaginationControls = isEnabled;\n        this._changeDetectorRef.markForCheck();\n      }\n    }\n  }\n  /**\n   * Evaluate whether the before and after controls should be enabled or disabled.\n   * If the header is at the beginning of the list (scroll distance is equal to 0) then disable the\n   * before button. If the header is at the end of the list (scroll distance is equal to the\n   * maximum distance we can scroll), then disable the after button.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n  _checkScrollingControls() {\n    if (this.disablePagination) {\n      this._disableScrollAfter = this._disableScrollBefore = true;\n    } else {\n      // Check if the pagination arrows should be activated.\n      this._disableScrollBefore = this.scrollDistance == 0;\n      this._disableScrollAfter = this.scrollDistance == this._getMaxScrollDistance();\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /**\n   * Determines what is the maximum length in pixels that can be set for the scroll distance. This\n   * is equal to the difference in width between the tab list container and tab header container.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n  _getMaxScrollDistance() {\n    const lengthOfTabList = this._tabListInner.nativeElement.scrollWidth;\n    const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n    return lengthOfTabList - viewLength || 0;\n  }\n  /** Tells the ink-bar to align itself to the current label wrapper */\n  _alignInkBarToSelectedTab() {\n    const selectedItem = this._items && this._items.length ? this._items.toArray()[this.selectedIndex] : null;\n    const selectedLabelWrapper = selectedItem ? selectedItem.elementRef.nativeElement : null;\n    if (selectedLabelWrapper) {\n      this._inkBar.alignToElement(selectedLabelWrapper);\n    } else {\n      this._inkBar.hide();\n    }\n  }\n  /** Stops the currently-running paginator interval.  */\n  _stopInterval() {\n    this._stopScrolling.next();\n  }\n  /**\n   * Handles the user pressing down on one of the paginators.\n   * Starts scrolling the header after a certain amount of time.\n   * @param direction In which direction the paginator should be scrolled.\n   */\n  _handlePaginatorPress(direction, mouseEvent) {\n    // Don't start auto scrolling for right mouse button clicks. Note that we shouldn't have to\n    // null check the `button`, but we do it so we don't break tests that use fake events.\n    if (mouseEvent && mouseEvent.button != null && mouseEvent.button !== 0) {\n      return;\n    }\n    // Avoid overlapping timers.\n    this._stopInterval();\n    // Start a timer after the delay and keep firing based on the interval.\n    timer(HEADER_SCROLL_DELAY, HEADER_SCROLL_INTERVAL)\n    // Keep the timer going until something tells it to stop or the component is destroyed.\n    .pipe(takeUntil(merge(this._stopScrolling, this._destroyed))).subscribe(() => {\n      const {\n        maxScrollDistance,\n        distance\n      } = this._scrollHeader(direction);\n      // Stop the timer if we've reached the start or the end.\n      if (distance === 0 || distance >= maxScrollDistance) {\n        this._stopInterval();\n      }\n    });\n  }\n  /**\n   * Scrolls the header to a given position.\n   * @param position Position to which to scroll.\n   * @returns Information on the current scroll distance and the maximum.\n   */\n  _scrollTo(position) {\n    if (this.disablePagination) {\n      return {\n        maxScrollDistance: 0,\n        distance: 0\n      };\n    }\n    const maxScrollDistance = this._getMaxScrollDistance();\n    this._scrollDistance = Math.max(0, Math.min(maxScrollDistance, position));\n    // Mark that the scroll distance has changed so that after the view is checked, the CSS\n    // transformation can move the header.\n    this._scrollDistanceChanged = true;\n    this._checkScrollingControls();\n    return {\n      maxScrollDistance,\n      distance: this._scrollDistance\n    };\n  }\n  static ɵfac = function MatPaginatedTabHeader_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatPaginatedTabHeader)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatPaginatedTabHeader,\n    inputs: {\n      disablePagination: [2, \"disablePagination\", \"disablePagination\", booleanAttribute],\n      selectedIndex: [2, \"selectedIndex\", \"selectedIndex\", numberAttribute]\n    },\n    outputs: {\n      selectFocusedIndex: \"selectFocusedIndex\",\n      indexFocused: \"indexFocused\"\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPaginatedTabHeader, [{\n    type: Directive\n  }], () => [], {\n    disablePagination: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    selectedIndex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    selectFocusedIndex: [{\n      type: Output\n    }],\n    indexFocused: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * The header of the tab group which displays a list of all the tabs in the tab group. Includes\n * an ink bar that follows the currently selected tab. When the tabs list's width exceeds the\n * width of the header container, then arrows will be displayed to allow the user to scroll\n * left and right across the header.\n * @docs-private\n */\nclass MatTabHeader extends MatPaginatedTabHeader {\n  _items;\n  _tabListContainer;\n  _tabList;\n  _tabListInner;\n  _nextPaginator;\n  _previousPaginator;\n  _inkBar;\n  /** Aria label of the header. */\n  ariaLabel;\n  /** Sets the `aria-labelledby` of the header. */\n  ariaLabelledby;\n  /** Whether the ripple effect is disabled or not. */\n  disableRipple = false;\n  ngAfterContentInit() {\n    this._inkBar = new MatInkBar(this._items);\n    super.ngAfterContentInit();\n  }\n  _itemSelected(event) {\n    event.preventDefault();\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatTabHeader_BaseFactory;\n    return function MatTabHeader_Factory(__ngFactoryType__) {\n      return (ɵMatTabHeader_BaseFactory || (ɵMatTabHeader_BaseFactory = i0.ɵɵgetInheritedFactory(MatTabHeader)))(__ngFactoryType__ || MatTabHeader);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatTabHeader,\n    selectors: [[\"mat-tab-header\"]],\n    contentQueries: function MatTabHeader_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatTabLabelWrapper, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._items = _t);\n      }\n    },\n    viewQuery: function MatTabHeader_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c1, 7);\n        i0.ɵɵviewQuery(_c2, 7);\n        i0.ɵɵviewQuery(_c3, 7);\n        i0.ɵɵviewQuery(_c4, 5);\n        i0.ɵɵviewQuery(_c5, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabListContainer = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabList = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabListInner = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._nextPaginator = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._previousPaginator = _t.first);\n      }\n    },\n    hostAttrs: [1, \"mat-mdc-tab-header\"],\n    hostVars: 4,\n    hostBindings: function MatTabHeader_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-controls-enabled\", ctx._showPaginationControls)(\"mat-mdc-tab-header-rtl\", ctx._getLayoutDirection() == \"rtl\");\n      }\n    },\n    inputs: {\n      ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n      ariaLabelledby: [0, \"aria-labelledby\", \"ariaLabelledby\"],\n      disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute]\n    },\n    features: [i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c0,\n    decls: 13,\n    vars: 10,\n    consts: [[\"previousPaginator\", \"\"], [\"tabListContainer\", \"\"], [\"tabList\", \"\"], [\"tabListInner\", \"\"], [\"nextPaginator\", \"\"], [\"mat-ripple\", \"\", 1, \"mat-mdc-tab-header-pagination\", \"mat-mdc-tab-header-pagination-before\", 3, \"click\", \"mousedown\", \"touchend\", \"matRippleDisabled\"], [1, \"mat-mdc-tab-header-pagination-chevron\"], [1, \"mat-mdc-tab-label-container\", 3, \"keydown\"], [\"role\", \"tablist\", 1, \"mat-mdc-tab-list\", 3, \"cdkObserveContent\"], [1, \"mat-mdc-tab-labels\"], [\"mat-ripple\", \"\", 1, \"mat-mdc-tab-header-pagination\", \"mat-mdc-tab-header-pagination-after\", 3, \"mousedown\", \"click\", \"touchend\", \"matRippleDisabled\"]],\n    template: function MatTabHeader_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 5, 0);\n        i0.ɵɵlistener(\"click\", function MatTabHeader_Template_div_click_0_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._handlePaginatorClick(\"before\"));\n        })(\"mousedown\", function MatTabHeader_Template_div_mousedown_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._handlePaginatorPress(\"before\", $event));\n        })(\"touchend\", function MatTabHeader_Template_div_touchend_0_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._stopInterval());\n        });\n        i0.ɵɵelement(2, \"div\", 6);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"div\", 7, 1);\n        i0.ɵɵlistener(\"keydown\", function MatTabHeader_Template_div_keydown_3_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._handleKeydown($event));\n        });\n        i0.ɵɵelementStart(5, \"div\", 8, 2);\n        i0.ɵɵlistener(\"cdkObserveContent\", function MatTabHeader_Template_div_cdkObserveContent_5_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._onContentChanges());\n        });\n        i0.ɵɵelementStart(7, \"div\", 9, 3);\n        i0.ɵɵprojection(9);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(10, \"div\", 10, 4);\n        i0.ɵɵlistener(\"mousedown\", function MatTabHeader_Template_div_mousedown_10_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._handlePaginatorPress(\"after\", $event));\n        })(\"click\", function MatTabHeader_Template_div_click_10_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._handlePaginatorClick(\"after\"));\n        })(\"touchend\", function MatTabHeader_Template_div_touchend_10_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._stopInterval());\n        });\n        i0.ɵɵelement(12, \"div\", 6);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-disabled\", ctx._disableScrollBefore);\n        i0.ɵɵproperty(\"matRippleDisabled\", ctx._disableScrollBefore || ctx.disableRipple);\n        i0.ɵɵadvance(3);\n        i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationsDisabled);\n        i0.ɵɵadvance(2);\n        i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel || null)(\"aria-labelledby\", ctx.ariaLabelledby || null);\n        i0.ɵɵadvance(5);\n        i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-disabled\", ctx._disableScrollAfter);\n        i0.ɵɵproperty(\"matRippleDisabled\", ctx._disableScrollAfter || ctx.disableRipple);\n      }\n    },\n    dependencies: [MatRipple, CdkObserveContent],\n    styles: [\".mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;outline:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-pagination-icon-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}.mat-mdc-tab-label-container{display:flex;flex-grow:1;overflow:hidden;z-index:1;border-bottom-style:solid;border-bottom-width:var(--mat-tab-divider-height, 1px);border-bottom-color:var(--mat-tab-divider-color, var(--mat-sys-surface-variant))}.mat-mdc-tab-group-inverted-header .mat-mdc-tab-label-container{border-bottom:none;border-top-style:solid;border-top-width:var(--mat-tab-divider-height, 1px);border-top-color:var(--mat-tab-divider-color, var(--mat-sys-surface-variant))}.mat-mdc-tab-labels{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:flex-end}.cdk-drop-list .mat-mdc-tab-labels,.mat-mdc-tab-labels.cdk-drop-list{min-height:var(--mat-tab-container-height, 48px)}.mat-mdc-tab::before{margin:5px}@media(forced-colors: active){.mat-mdc-tab[aria-disabled=true]{color:GrayText}}\\n\"],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabHeader, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tab-header',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      host: {\n        'class': 'mat-mdc-tab-header',\n        '[class.mat-mdc-tab-header-pagination-controls-enabled]': '_showPaginationControls',\n        '[class.mat-mdc-tab-header-rtl]': \"_getLayoutDirection() == 'rtl'\"\n      },\n      imports: [MatRipple, CdkObserveContent],\n      template: \"<!--\\n Note that this intentionally uses a `div` instead of a `button`, because it's not part of\\n the regular tabs flow and is only here to support mouse users. It should also not be focusable.\\n-->\\n<div class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-before\\\"\\n     #previousPaginator\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</div>\\n\\n<div\\n  class=\\\"mat-mdc-tab-label-container\\\"\\n  #tabListContainer\\n  (keydown)=\\\"_handleKeydown($event)\\\"\\n  [class._mat-animation-noopable]=\\\"_animationsDisabled\\\">\\n  <div\\n    #tabList\\n    class=\\\"mat-mdc-tab-list\\\"\\n    role=\\\"tablist\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"ariaLabelledby || null\\\"\\n    (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-mdc-tab-labels\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</div>\\n\\n<div class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-after\\\"\\n     #nextPaginator\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</div>\\n\",\n      styles: [\".mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;outline:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-pagination-icon-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}.mat-mdc-tab-label-container{display:flex;flex-grow:1;overflow:hidden;z-index:1;border-bottom-style:solid;border-bottom-width:var(--mat-tab-divider-height, 1px);border-bottom-color:var(--mat-tab-divider-color, var(--mat-sys-surface-variant))}.mat-mdc-tab-group-inverted-header .mat-mdc-tab-label-container{border-bottom:none;border-top-style:solid;border-top-width:var(--mat-tab-divider-height, 1px);border-top-color:var(--mat-tab-divider-color, var(--mat-sys-surface-variant))}.mat-mdc-tab-labels{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:flex-end}.cdk-drop-list .mat-mdc-tab-labels,.mat-mdc-tab-labels.cdk-drop-list{min-height:var(--mat-tab-container-height, 48px)}.mat-mdc-tab::before{margin:5px}@media(forced-colors: active){.mat-mdc-tab[aria-disabled=true]{color:GrayText}}\\n\"]\n    }]\n  }], null, {\n    _items: [{\n      type: ContentChildren,\n      args: [MatTabLabelWrapper, {\n        descendants: false\n      }]\n    }],\n    _tabListContainer: [{\n      type: ViewChild,\n      args: ['tabListContainer', {\n        static: true\n      }]\n    }],\n    _tabList: [{\n      type: ViewChild,\n      args: ['tabList', {\n        static: true\n      }]\n    }],\n    _tabListInner: [{\n      type: ViewChild,\n      args: ['tabListInner', {\n        static: true\n      }]\n    }],\n    _nextPaginator: [{\n      type: ViewChild,\n      args: ['nextPaginator']\n    }],\n    _previousPaginator: [{\n      type: ViewChild,\n      args: ['previousPaginator']\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/** Injection token that can be used to provide the default options the tabs module. */\nconst MAT_TABS_CONFIG = new InjectionToken('MAT_TABS_CONFIG');\n\n/**\n * The portal host directive for the contents of the tab.\n * @docs-private\n */\nclass MatTabBodyPortal extends CdkPortalOutlet {\n  _host = inject(MatTabBody);\n  /** Subscription to events for when the tab body begins centering. */\n  _centeringSub = Subscription.EMPTY;\n  /** Subscription to events for when the tab body finishes leaving from center position. */\n  _leavingSub = Subscription.EMPTY;\n  constructor() {\n    super();\n  }\n  /** Set initial visibility or set up subscription for changing visibility. */\n  ngOnInit() {\n    super.ngOnInit();\n    this._centeringSub = this._host._beforeCentering.pipe(startWith(this._host._isCenterPosition())).subscribe(isCentering => {\n      if (this._host._content && isCentering && !this.hasAttached()) {\n        this.attach(this._host._content);\n      }\n    });\n    this._leavingSub = this._host._afterLeavingCenter.subscribe(() => {\n      if (!this._host.preserveContent) {\n        this.detach();\n      }\n    });\n  }\n  /** Clean up centering subscription. */\n  ngOnDestroy() {\n    super.ngOnDestroy();\n    this._centeringSub.unsubscribe();\n    this._leavingSub.unsubscribe();\n  }\n  static ɵfac = function MatTabBodyPortal_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatTabBodyPortal)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatTabBodyPortal,\n    selectors: [[\"\", \"matTabBodyHost\", \"\"]],\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabBodyPortal, [{\n    type: Directive,\n    args: [{\n      selector: '[matTabBodyHost]'\n    }]\n  }], () => [], null);\n})();\n/**\n * Wrapper for the contents of a tab.\n * @docs-private\n */\nclass MatTabBody {\n  _elementRef = inject(ElementRef);\n  _dir = inject(Directionality, {\n    optional: true\n  });\n  _ngZone = inject(NgZone);\n  _injector = inject(Injector);\n  _renderer = inject(Renderer2);\n  _diAnimationsDisabled = _animationsDisabled();\n  _eventCleanups;\n  _initialized;\n  _fallbackTimer;\n  /** Current position of the tab-body in the tab-group. Zero means that the tab is visible. */\n  _positionIndex;\n  /** Subscription to the directionality change observable. */\n  _dirChangeSubscription = Subscription.EMPTY;\n  /** Current position of the body within the tab group. */\n  _position;\n  /** Previous position of the body. */\n  _previousPosition;\n  /** Event emitted when the tab begins to animate towards the center as the active tab. */\n  _onCentering = new EventEmitter();\n  /** Event emitted before the centering of the tab begins. */\n  _beforeCentering = new EventEmitter();\n  /** Event emitted before the centering of the tab begins. */\n  _afterLeavingCenter = new EventEmitter();\n  /** Event emitted when the tab completes its animation towards the center. */\n  _onCentered = new EventEmitter(true);\n  /** The portal host inside of this container into which the tab body content will be loaded. */\n  _portalHost;\n  /** Element in which the content is rendered. */\n  _contentElement;\n  /** The tab body content to display. */\n  _content;\n  // Note that the default value will always be overwritten by `MatTabBody`, but we need one\n  // anyway to prevent the animations module from throwing an error if the body is used on its own.\n  /** Duration for the tab's animation. */\n  animationDuration = '500ms';\n  /** Whether the tab's content should be kept in the DOM while it's off-screen. */\n  preserveContent = false;\n  /** The shifted index position of the tab body, where zero represents the active center tab. */\n  set position(position) {\n    this._positionIndex = position;\n    this._computePositionAnimationState();\n  }\n  constructor() {\n    if (this._dir) {\n      const changeDetectorRef = inject(ChangeDetectorRef);\n      this._dirChangeSubscription = this._dir.change.subscribe(dir => {\n        this._computePositionAnimationState(dir);\n        changeDetectorRef.markForCheck();\n      });\n    }\n  }\n  ngOnInit() {\n    this._bindTransitionEvents();\n    if (this._position === 'center') {\n      this._setActiveClass(true);\n      // Allows for the dynamic height to animate properly on the initial run.\n      afterNextRender(() => this._onCentering.emit(this._elementRef.nativeElement.clientHeight), {\n        injector: this._injector\n      });\n    }\n    this._initialized = true;\n  }\n  ngOnDestroy() {\n    clearTimeout(this._fallbackTimer);\n    this._eventCleanups?.forEach(cleanup => cleanup());\n    this._dirChangeSubscription.unsubscribe();\n  }\n  /** Sets up the transition events. */\n  _bindTransitionEvents() {\n    this._ngZone.runOutsideAngular(() => {\n      const element = this._elementRef.nativeElement;\n      const transitionDone = event => {\n        if (event.target === this._contentElement?.nativeElement) {\n          this._elementRef.nativeElement.classList.remove('mat-tab-body-animating');\n          // Only fire the actual callback when a transition is fully finished,\n          // otherwise the content can jump around when the next transition starts.\n          if (event.type === 'transitionend') {\n            this._transitionDone();\n          }\n        }\n      };\n      this._eventCleanups = [this._renderer.listen(element, 'transitionstart', event => {\n        if (event.target === this._contentElement?.nativeElement) {\n          this._elementRef.nativeElement.classList.add('mat-tab-body-animating');\n          this._transitionStarted();\n        }\n      }), this._renderer.listen(element, 'transitionend', transitionDone), this._renderer.listen(element, 'transitioncancel', transitionDone)];\n    });\n  }\n  /** Called when a transition has started. */\n  _transitionStarted() {\n    clearTimeout(this._fallbackTimer);\n    const isCentering = this._position === 'center';\n    this._beforeCentering.emit(isCentering);\n    if (isCentering) {\n      this._onCentering.emit(this._elementRef.nativeElement.clientHeight);\n    }\n  }\n  /** Called when a transition is done. */\n  _transitionDone() {\n    if (this._position === 'center') {\n      this._onCentered.emit();\n    } else if (this._previousPosition === 'center') {\n      this._afterLeavingCenter.emit();\n    }\n  }\n  /** Sets the active styling on the tab body based on its current position. */\n  _setActiveClass(isActive) {\n    this._elementRef.nativeElement.classList.toggle('mat-mdc-tab-body-active', isActive);\n  }\n  /** The text direction of the containing app. */\n  _getLayoutDirection() {\n    return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n  }\n  /** Whether the provided position state is considered center, regardless of origin. */\n  _isCenterPosition() {\n    return this._positionIndex === 0;\n  }\n  /** Computes the position state that will be used for the tab-body animation trigger. */\n  _computePositionAnimationState(dir = this._getLayoutDirection()) {\n    this._previousPosition = this._position;\n    if (this._positionIndex < 0) {\n      this._position = dir == 'ltr' ? 'left' : 'right';\n    } else if (this._positionIndex > 0) {\n      this._position = dir == 'ltr' ? 'right' : 'left';\n    } else {\n      this._position = 'center';\n    }\n    if (this._animationsDisabled()) {\n      this._simulateTransitionEvents();\n    } else if (this._initialized && (this._position === 'center' || this._previousPosition === 'center')) {\n      // The transition events are load-bearing and in some cases they might not fire (e.g.\n      // tests setting `* {transition: none}` to disable animations). This timeout will simulate\n      // them if a transition doesn't start within a certain amount of time.\n      clearTimeout(this._fallbackTimer);\n      this._fallbackTimer = this._ngZone.runOutsideAngular(() => setTimeout(() => this._simulateTransitionEvents(), 100));\n    }\n  }\n  /** Simulates the body's transition events in an environment where they might not fire. */\n  _simulateTransitionEvents() {\n    this._transitionStarted();\n    afterNextRender(() => this._transitionDone(), {\n      injector: this._injector\n    });\n  }\n  /** Whether animations are disabled for the tab group. */\n  _animationsDisabled() {\n    return this._diAnimationsDisabled || this.animationDuration === '0ms' || this.animationDuration === '0s';\n  }\n  static ɵfac = function MatTabBody_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatTabBody)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatTabBody,\n    selectors: [[\"mat-tab-body\"]],\n    viewQuery: function MatTabBody_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(MatTabBodyPortal, 5);\n        i0.ɵɵviewQuery(_c6, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._portalHost = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._contentElement = _t.first);\n      }\n    },\n    hostAttrs: [1, \"mat-mdc-tab-body\"],\n    hostVars: 1,\n    hostBindings: function MatTabBody_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"inert\", ctx._position === \"center\" ? null : \"\");\n      }\n    },\n    inputs: {\n      _content: [0, \"content\", \"_content\"],\n      animationDuration: \"animationDuration\",\n      preserveContent: \"preserveContent\",\n      position: \"position\"\n    },\n    outputs: {\n      _onCentering: \"_onCentering\",\n      _beforeCentering: \"_beforeCentering\",\n      _onCentered: \"_onCentered\"\n    },\n    decls: 3,\n    vars: 6,\n    consts: [[\"content\", \"\"], [\"cdkScrollable\", \"\", 1, \"mat-mdc-tab-body-content\"], [\"matTabBodyHost\", \"\"]],\n    template: function MatTabBody_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 1, 0);\n        i0.ɵɵtemplate(2, MatTabBody_ng_template_2_Template, 0, 0, \"ng-template\", 2);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-tab-body-content-left\", ctx._position === \"left\")(\"mat-tab-body-content-right\", ctx._position === \"right\")(\"mat-tab-body-content-can-animate\", ctx._position === \"center\" || ctx._previousPosition === \"center\");\n      }\n    },\n    dependencies: [MatTabBodyPortal, CdkScrollable],\n    styles: [\".mat-mdc-tab-body{top:0;left:0;right:0;bottom:0;position:absolute;display:block;overflow:hidden;outline:0;flex-basis:100%}.mat-mdc-tab-body.mat-mdc-tab-body-active{position:relative;overflow-x:hidden;overflow-y:auto;z-index:1;flex-grow:1}.mat-mdc-tab-group.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body.mat-mdc-tab-body-active{overflow-y:hidden}.mat-mdc-tab-body-content{height:100%;overflow:auto;transform:none;visibility:hidden}.mat-tab-body-animating>.mat-mdc-tab-body-content,.mat-mdc-tab-body-active>.mat-mdc-tab-body-content{visibility:visible}.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body-content{overflow:hidden}.mat-tab-body-content-can-animate{transition:transform var(--mat-tab-animation-duration) 1ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable .mat-tab-body-content-can-animate{transition:none}.mat-tab-body-content-left{transform:translate3d(-100%, 0, 0)}.mat-tab-body-content-right{transform:translate3d(100%, 0, 0)}\\n\"],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabBody, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tab-body',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      host: {\n        'class': 'mat-mdc-tab-body',\n        // In most cases the `visibility: hidden` that we set on the off-screen content is enough\n        // to stop interactions with it, but if a child element sets its own `visibility`, it'll\n        // override the one from the parent. This ensures that even those elements will be removed\n        // from the accessibility tree.\n        '[attr.inert]': '_position === \"center\" ? null : \"\"'\n      },\n      imports: [MatTabBodyPortal, CdkScrollable],\n      template: \"<div\\n   class=\\\"mat-mdc-tab-body-content\\\"\\n   #content\\n   cdkScrollable\\n   [class.mat-tab-body-content-left]=\\\"_position === 'left'\\\"\\n   [class.mat-tab-body-content-right]=\\\"_position === 'right'\\\"\\n   [class.mat-tab-body-content-can-animate]=\\\"_position === 'center' || _previousPosition === 'center'\\\">\\n  <ng-template matTabBodyHost></ng-template>\\n</div>\\n\",\n      styles: [\".mat-mdc-tab-body{top:0;left:0;right:0;bottom:0;position:absolute;display:block;overflow:hidden;outline:0;flex-basis:100%}.mat-mdc-tab-body.mat-mdc-tab-body-active{position:relative;overflow-x:hidden;overflow-y:auto;z-index:1;flex-grow:1}.mat-mdc-tab-group.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body.mat-mdc-tab-body-active{overflow-y:hidden}.mat-mdc-tab-body-content{height:100%;overflow:auto;transform:none;visibility:hidden}.mat-tab-body-animating>.mat-mdc-tab-body-content,.mat-mdc-tab-body-active>.mat-mdc-tab-body-content{visibility:visible}.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body-content{overflow:hidden}.mat-tab-body-content-can-animate{transition:transform var(--mat-tab-animation-duration) 1ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable .mat-tab-body-content-can-animate{transition:none}.mat-tab-body-content-left{transform:translate3d(-100%, 0, 0)}.mat-tab-body-content-right{transform:translate3d(100%, 0, 0)}\\n\"]\n    }]\n  }], () => [], {\n    _onCentering: [{\n      type: Output\n    }],\n    _beforeCentering: [{\n      type: Output\n    }],\n    _onCentered: [{\n      type: Output\n    }],\n    _portalHost: [{\n      type: ViewChild,\n      args: [MatTabBodyPortal]\n    }],\n    _contentElement: [{\n      type: ViewChild,\n      args: ['content']\n    }],\n    _content: [{\n      type: Input,\n      args: ['content']\n    }],\n    animationDuration: [{\n      type: Input\n    }],\n    preserveContent: [{\n      type: Input\n    }],\n    position: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Material design tab-group component. Supports basic tab pairs (label + content) and includes\n * animated ink-bar, keyboard navigation, and screen reader.\n * See: https://material.io/design/components/tabs.html\n */\nclass MatTabGroup {\n  _elementRef = inject(ElementRef);\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _ngZone = inject(NgZone);\n  _tabsSubscription = Subscription.EMPTY;\n  _tabLabelSubscription = Subscription.EMPTY;\n  _tabBodySubscription = Subscription.EMPTY;\n  _diAnimationsDisabled = _animationsDisabled();\n  /**\n   * All tabs inside the tab group. This includes tabs that belong to groups that are nested\n   * inside the current one. We filter out only the tabs that belong to this group in `_tabs`.\n   */\n  _allTabs;\n  _tabBodies;\n  _tabBodyWrapper;\n  _tabHeader;\n  /** All of the tabs that belong to the group. */\n  _tabs = new QueryList();\n  /** The tab index that should be selected after the content has been checked. */\n  _indexToSelect = 0;\n  /** Index of the tab that was focused last. */\n  _lastFocusedTabIndex = null;\n  /** Snapshot of the height of the tab body wrapper before another tab is activated. */\n  _tabBodyWrapperHeight = 0;\n  /**\n   * Theme color of the tab group. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/tabs/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  color;\n  /** Whether the ink bar should fit its width to the size of the tab label content. */\n  get fitInkBarToContent() {\n    return this._fitInkBarToContent;\n  }\n  set fitInkBarToContent(value) {\n    this._fitInkBarToContent = value;\n    this._changeDetectorRef.markForCheck();\n  }\n  _fitInkBarToContent = false;\n  /** Whether tabs should be stretched to fill the header. */\n  stretchTabs = true;\n  /** Alignment for tabs label. */\n  alignTabs = null;\n  /** Whether the tab group should grow to the size of the active tab. */\n  dynamicHeight = false;\n  /** The index of the active tab. */\n  get selectedIndex() {\n    return this._selectedIndex;\n  }\n  set selectedIndex(value) {\n    this._indexToSelect = isNaN(value) ? null : value;\n  }\n  _selectedIndex = null;\n  /** Position of the tab header. */\n  headerPosition = 'above';\n  /** Duration for the tab animation. Will be normalized to milliseconds if no units are set. */\n  get animationDuration() {\n    return this._animationDuration;\n  }\n  set animationDuration(value) {\n    const stringValue = value + '';\n    this._animationDuration = /^\\d+$/.test(stringValue) ? value + 'ms' : stringValue;\n  }\n  _animationDuration;\n  /**\n   * `tabindex` to be set on the inner element that wraps the tab content. Can be used for improved\n   * accessibility when the tab does not have focusable elements or if it has scrollable content.\n   * The `tabindex` will be removed automatically for inactive tabs.\n   * Read more at https://www.w3.org/TR/wai-aria-practices/examples/tabs/tabs-2/tabs.html\n   */\n  get contentTabIndex() {\n    return this._contentTabIndex;\n  }\n  set contentTabIndex(value) {\n    this._contentTabIndex = isNaN(value) ? null : value;\n  }\n  _contentTabIndex;\n  /**\n   * Whether pagination should be disabled. This can be used to avoid unnecessary\n   * layout recalculations if it's known that pagination won't be required.\n   */\n  disablePagination = false;\n  /** Whether ripples in the tab group are disabled. */\n  disableRipple = false;\n  /**\n   * By default tabs remove their content from the DOM while it's off-screen.\n   * Setting this to `true` will keep it in the DOM which will prevent elements\n   * like iframes and videos from reloading next time it comes back into the view.\n   */\n  preserveContent = false;\n  /**\n   * Theme color of the background of the tab group. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/tabs/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   *\n   * @deprecated The background color should be customized through Sass theming APIs.\n   * @breaking-change 20.0.0 Remove this input\n   */\n  get backgroundColor() {\n    return this._backgroundColor;\n  }\n  set backgroundColor(value) {\n    const classList = this._elementRef.nativeElement.classList;\n    classList.remove('mat-tabs-with-background', `mat-background-${this.backgroundColor}`);\n    if (value) {\n      classList.add('mat-tabs-with-background', `mat-background-${value}`);\n    }\n    this._backgroundColor = value;\n  }\n  _backgroundColor;\n  /** Aria label of the inner `tablist` of the group. */\n  ariaLabel;\n  /** Sets the `aria-labelledby` of the inner `tablist` of the group. */\n  ariaLabelledby;\n  /** Output to enable support for two-way binding on `[(selectedIndex)]` */\n  selectedIndexChange = new EventEmitter();\n  /** Event emitted when focus has changed within a tab group. */\n  focusChange = new EventEmitter();\n  /** Event emitted when the body animation has completed */\n  animationDone = new EventEmitter();\n  /** Event emitted when the tab selection has changed. */\n  selectedTabChange = new EventEmitter(true);\n  _groupId;\n  /** Whether the tab group is rendered on the server. */\n  _isServer = !inject(Platform).isBrowser;\n  constructor() {\n    const defaultConfig = inject(MAT_TABS_CONFIG, {\n      optional: true\n    });\n    this._groupId = inject(_IdGenerator).getId('mat-tab-group-');\n    this.animationDuration = defaultConfig && defaultConfig.animationDuration ? defaultConfig.animationDuration : '500ms';\n    this.disablePagination = defaultConfig && defaultConfig.disablePagination != null ? defaultConfig.disablePagination : false;\n    this.dynamicHeight = defaultConfig && defaultConfig.dynamicHeight != null ? defaultConfig.dynamicHeight : false;\n    if (defaultConfig?.contentTabIndex != null) {\n      this.contentTabIndex = defaultConfig.contentTabIndex;\n    }\n    this.preserveContent = !!defaultConfig?.preserveContent;\n    this.fitInkBarToContent = defaultConfig && defaultConfig.fitInkBarToContent != null ? defaultConfig.fitInkBarToContent : false;\n    this.stretchTabs = defaultConfig && defaultConfig.stretchTabs != null ? defaultConfig.stretchTabs : true;\n    this.alignTabs = defaultConfig && defaultConfig.alignTabs != null ? defaultConfig.alignTabs : null;\n  }\n  /**\n   * After the content is checked, this component knows what tabs have been defined\n   * and what the selected index should be. This is where we can know exactly what position\n   * each tab should be in according to the new selected index, and additionally we know how\n   * a new selected tab should transition in (from the left or right).\n   */\n  ngAfterContentChecked() {\n    // Don't clamp the `indexToSelect` immediately in the setter because it can happen that\n    // the amount of tabs changes before the actual change detection runs.\n    const indexToSelect = this._indexToSelect = this._clampTabIndex(this._indexToSelect);\n    // If there is a change in selected index, emit a change event. Should not trigger if\n    // the selected index has not yet been initialized.\n    if (this._selectedIndex != indexToSelect) {\n      const isFirstRun = this._selectedIndex == null;\n      if (!isFirstRun) {\n        this.selectedTabChange.emit(this._createChangeEvent(indexToSelect));\n        // Preserve the height so page doesn't scroll up during tab change.\n        // Fixes https://stackblitz.com/edit/mat-tabs-scroll-page-top-on-tab-change\n        const wrapper = this._tabBodyWrapper.nativeElement;\n        wrapper.style.minHeight = wrapper.clientHeight + 'px';\n      }\n      // Changing these values after change detection has run\n      // since the checked content may contain references to them.\n      Promise.resolve().then(() => {\n        this._tabs.forEach((tab, index) => tab.isActive = index === indexToSelect);\n        if (!isFirstRun) {\n          this.selectedIndexChange.emit(indexToSelect);\n          // Clear the min-height, this was needed during tab change to avoid\n          // unnecessary scrolling.\n          this._tabBodyWrapper.nativeElement.style.minHeight = '';\n        }\n      });\n    }\n    // Setup the position for each tab and optionally setup an origin on the next selected tab.\n    this._tabs.forEach((tab, index) => {\n      tab.position = index - indexToSelect;\n      // If there is already a selected tab, then set up an origin for the next selected tab\n      // if it doesn't have one already.\n      if (this._selectedIndex != null && tab.position == 0 && !tab.origin) {\n        tab.origin = indexToSelect - this._selectedIndex;\n      }\n    });\n    if (this._selectedIndex !== indexToSelect) {\n      this._selectedIndex = indexToSelect;\n      this._lastFocusedTabIndex = null;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  ngAfterContentInit() {\n    this._subscribeToAllTabChanges();\n    this._subscribeToTabLabels();\n    // Subscribe to changes in the amount of tabs, in order to be\n    // able to re-render the content as new tabs are added or removed.\n    this._tabsSubscription = this._tabs.changes.subscribe(() => {\n      const indexToSelect = this._clampTabIndex(this._indexToSelect);\n      // Maintain the previously-selected tab if a new tab is added or removed and there is no\n      // explicit change that selects a different tab.\n      if (indexToSelect === this._selectedIndex) {\n        const tabs = this._tabs.toArray();\n        let selectedTab;\n        for (let i = 0; i < tabs.length; i++) {\n          if (tabs[i].isActive) {\n            // Assign both to the `_indexToSelect` and `_selectedIndex` so we don't fire a changed\n            // event, otherwise the consumer may end up in an infinite loop in some edge cases like\n            // adding a tab within the `selectedIndexChange` event.\n            this._indexToSelect = this._selectedIndex = i;\n            this._lastFocusedTabIndex = null;\n            selectedTab = tabs[i];\n            break;\n          }\n        }\n        // If we haven't found an active tab and a tab exists at the selected index, it means\n        // that the active tab was swapped out. Since this won't be picked up by the rendering\n        // loop in `ngAfterContentChecked`, we need to sync it up manually.\n        if (!selectedTab && tabs[indexToSelect]) {\n          Promise.resolve().then(() => {\n            tabs[indexToSelect].isActive = true;\n            this.selectedTabChange.emit(this._createChangeEvent(indexToSelect));\n          });\n        }\n      }\n      this._changeDetectorRef.markForCheck();\n    });\n  }\n  ngAfterViewInit() {\n    this._tabBodySubscription = this._tabBodies.changes.subscribe(() => this._bodyCentered(true));\n  }\n  /** Listens to changes in all of the tabs. */\n  _subscribeToAllTabChanges() {\n    // Since we use a query with `descendants: true` to pick up the tabs, we may end up catching\n    // some that are inside of nested tab groups. We filter them out manually by checking that\n    // the closest group to the tab is the current one.\n    this._allTabs.changes.pipe(startWith(this._allTabs)).subscribe(tabs => {\n      this._tabs.reset(tabs.filter(tab => {\n        return tab._closestTabGroup === this || !tab._closestTabGroup;\n      }));\n      this._tabs.notifyOnChanges();\n    });\n  }\n  ngOnDestroy() {\n    this._tabs.destroy();\n    this._tabsSubscription.unsubscribe();\n    this._tabLabelSubscription.unsubscribe();\n    this._tabBodySubscription.unsubscribe();\n  }\n  /** Re-aligns the ink bar to the selected tab element. */\n  realignInkBar() {\n    if (this._tabHeader) {\n      this._tabHeader._alignInkBarToSelectedTab();\n    }\n  }\n  /**\n   * Recalculates the tab group's pagination dimensions.\n   *\n   * WARNING: Calling this method can be very costly in terms of performance. It should be called\n   * as infrequently as possible from outside of the Tabs component as it causes a reflow of the\n   * page.\n   */\n  updatePagination() {\n    if (this._tabHeader) {\n      this._tabHeader.updatePagination();\n    }\n  }\n  /**\n   * Sets focus to a particular tab.\n   * @param index Index of the tab to be focused.\n   */\n  focusTab(index) {\n    const header = this._tabHeader;\n    if (header) {\n      header.focusIndex = index;\n    }\n  }\n  _focusChanged(index) {\n    this._lastFocusedTabIndex = index;\n    this.focusChange.emit(this._createChangeEvent(index));\n  }\n  _createChangeEvent(index) {\n    const event = new MatTabChangeEvent();\n    event.index = index;\n    if (this._tabs && this._tabs.length) {\n      event.tab = this._tabs.toArray()[index];\n    }\n    return event;\n  }\n  /**\n   * Subscribes to changes in the tab labels. This is needed, because the @Input for the label is\n   * on the MatTab component, whereas the data binding is inside the MatTabGroup. In order for the\n   * binding to be updated, we need to subscribe to changes in it and trigger change detection\n   * manually.\n   */\n  _subscribeToTabLabels() {\n    if (this._tabLabelSubscription) {\n      this._tabLabelSubscription.unsubscribe();\n    }\n    this._tabLabelSubscription = merge(...this._tabs.map(tab => tab._stateChanges)).subscribe(() => this._changeDetectorRef.markForCheck());\n  }\n  /** Clamps the given index to the bounds of 0 and the tabs length. */\n  _clampTabIndex(index) {\n    // Note the `|| 0`, which ensures that values like NaN can't get through\n    // and which would otherwise throw the component into an infinite loop\n    // (since Math.max(NaN, 0) === NaN).\n    return Math.min(this._tabs.length - 1, Math.max(index || 0, 0));\n  }\n  /** Returns a unique id for each tab label element */\n  _getTabLabelId(tab, index) {\n    return tab.id || `${this._groupId}-label-${index}`;\n  }\n  /** Returns a unique id for each tab content element */\n  _getTabContentId(index) {\n    return `${this._groupId}-content-${index}`;\n  }\n  /**\n   * Sets the height of the body wrapper to the height of the activating tab if dynamic\n   * height property is true.\n   */\n  _setTabBodyWrapperHeight(tabHeight) {\n    if (!this.dynamicHeight || !this._tabBodyWrapperHeight) {\n      this._tabBodyWrapperHeight = tabHeight;\n      return;\n    }\n    const wrapper = this._tabBodyWrapper.nativeElement;\n    wrapper.style.height = this._tabBodyWrapperHeight + 'px';\n    // This conditional forces the browser to paint the height so that\n    // the animation to the new height can have an origin.\n    if (this._tabBodyWrapper.nativeElement.offsetHeight) {\n      wrapper.style.height = tabHeight + 'px';\n    }\n  }\n  /** Removes the height of the tab body wrapper. */\n  _removeTabBodyWrapperHeight() {\n    const wrapper = this._tabBodyWrapper.nativeElement;\n    this._tabBodyWrapperHeight = wrapper.clientHeight;\n    wrapper.style.height = '';\n    this._ngZone.run(() => this.animationDone.emit());\n  }\n  /** Handle click events, setting new selected index if appropriate. */\n  _handleClick(tab, tabHeader, index) {\n    tabHeader.focusIndex = index;\n    if (!tab.disabled) {\n      this.selectedIndex = index;\n    }\n  }\n  /** Retrieves the tabindex for the tab. */\n  _getTabIndex(index) {\n    const targetIndex = this._lastFocusedTabIndex ?? this.selectedIndex;\n    return index === targetIndex ? 0 : -1;\n  }\n  /** Callback for when the focused state of a tab has changed. */\n  _tabFocusChanged(focusOrigin, index) {\n    // Mouse/touch focus happens during the `mousedown`/`touchstart` phase which\n    // can cause the tab to be moved out from under the pointer, interrupting the\n    // click sequence (see #21898). We don't need to scroll the tab into view for\n    // such cases anyway, because it will be done when the tab becomes selected.\n    if (focusOrigin && focusOrigin !== 'mouse' && focusOrigin !== 'touch') {\n      this._tabHeader.focusIndex = index;\n    }\n  }\n  /**\n   * Callback invoked when the centered state of a tab body changes.\n   * @param isCenter Whether the tab will be in the center.\n   */\n  _bodyCentered(isCenter) {\n    // Marks all the existing tabs as inactive and the center tab as active. Note that this can\n    // be achieved much easier by using a class binding on each body. The problem with\n    // doing so is that we can't control the timing of when the class is removed from the\n    // previously-active element and added to the newly-active one. If there's a tick between\n    // removing the class and adding the new one, the content will jump in a very jarring way.\n    // We go through the trouble of setting the classes ourselves to guarantee that they're\n    // swapped out at the same time.\n    if (isCenter) {\n      this._tabBodies?.forEach((body, i) => body._setActiveClass(i === this._selectedIndex));\n    }\n  }\n  _animationsDisabled() {\n    return this._diAnimationsDisabled || this.animationDuration === '0' || this.animationDuration === '0ms';\n  }\n  static ɵfac = function MatTabGroup_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatTabGroup)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatTabGroup,\n    selectors: [[\"mat-tab-group\"]],\n    contentQueries: function MatTabGroup_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatTab, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allTabs = _t);\n      }\n    },\n    viewQuery: function MatTabGroup_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c7, 5);\n        i0.ɵɵviewQuery(_c8, 5);\n        i0.ɵɵviewQuery(MatTabBody, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabBodyWrapper = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabHeader = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabBodies = _t);\n      }\n    },\n    hostAttrs: [1, \"mat-mdc-tab-group\"],\n    hostVars: 11,\n    hostBindings: function MatTabGroup_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"mat-align-tabs\", ctx.alignTabs);\n        i0.ɵɵclassMap(\"mat-\" + (ctx.color || \"primary\"));\n        i0.ɵɵstyleProp(\"--mat-tab-animation-duration\", ctx.animationDuration);\n        i0.ɵɵclassProp(\"mat-mdc-tab-group-dynamic-height\", ctx.dynamicHeight)(\"mat-mdc-tab-group-inverted-header\", ctx.headerPosition === \"below\")(\"mat-mdc-tab-group-stretch-tabs\", ctx.stretchTabs);\n      }\n    },\n    inputs: {\n      color: \"color\",\n      fitInkBarToContent: [2, \"fitInkBarToContent\", \"fitInkBarToContent\", booleanAttribute],\n      stretchTabs: [2, \"mat-stretch-tabs\", \"stretchTabs\", booleanAttribute],\n      alignTabs: [0, \"mat-align-tabs\", \"alignTabs\"],\n      dynamicHeight: [2, \"dynamicHeight\", \"dynamicHeight\", booleanAttribute],\n      selectedIndex: [2, \"selectedIndex\", \"selectedIndex\", numberAttribute],\n      headerPosition: \"headerPosition\",\n      animationDuration: \"animationDuration\",\n      contentTabIndex: [2, \"contentTabIndex\", \"contentTabIndex\", numberAttribute],\n      disablePagination: [2, \"disablePagination\", \"disablePagination\", booleanAttribute],\n      disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute],\n      preserveContent: [2, \"preserveContent\", \"preserveContent\", booleanAttribute],\n      backgroundColor: \"backgroundColor\",\n      ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n      ariaLabelledby: [0, \"aria-labelledby\", \"ariaLabelledby\"]\n    },\n    outputs: {\n      selectedIndexChange: \"selectedIndexChange\",\n      focusChange: \"focusChange\",\n      animationDone: \"animationDone\",\n      selectedTabChange: \"selectedTabChange\"\n    },\n    exportAs: [\"matTabGroup\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_TAB_GROUP,\n      useExisting: MatTabGroup\n    }])],\n    ngContentSelectors: _c0,\n    decls: 9,\n    vars: 8,\n    consts: [[\"tabHeader\", \"\"], [\"tabBodyWrapper\", \"\"], [\"tabNode\", \"\"], [3, \"indexFocused\", \"selectFocusedIndex\", \"selectedIndex\", \"disableRipple\", \"disablePagination\", \"aria-label\", \"aria-labelledby\"], [\"role\", \"tab\", \"matTabLabelWrapper\", \"\", \"cdkMonitorElementFocus\", \"\", 1, \"mdc-tab\", \"mat-mdc-tab\", \"mat-focus-indicator\", 3, \"id\", \"mdc-tab--active\", \"class\", \"disabled\", \"fitInkBarToContent\"], [1, \"mat-mdc-tab-body-wrapper\"], [\"role\", \"tabpanel\", 3, \"id\", \"class\", \"content\", \"position\", \"animationDuration\", \"preserveContent\"], [\"role\", \"tab\", \"matTabLabelWrapper\", \"\", \"cdkMonitorElementFocus\", \"\", 1, \"mdc-tab\", \"mat-mdc-tab\", \"mat-focus-indicator\", 3, \"click\", \"cdkFocusChange\", \"id\", \"disabled\", \"fitInkBarToContent\"], [1, \"mdc-tab__ripple\"], [\"mat-ripple\", \"\", 1, \"mat-mdc-tab-ripple\", 3, \"matRippleTrigger\", \"matRippleDisabled\"], [1, \"mdc-tab__content\"], [1, \"mdc-tab__text-label\"], [3, \"cdkPortalOutlet\"], [\"role\", \"tabpanel\", 3, \"_onCentered\", \"_onCentering\", \"_beforeCentering\", \"id\", \"content\", \"position\", \"animationDuration\", \"preserveContent\"]],\n    template: function MatTabGroup_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"mat-tab-header\", 3, 0);\n        i0.ɵɵlistener(\"indexFocused\", function MatTabGroup_Template_mat_tab_header_indexFocused_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._focusChanged($event));\n        })(\"selectFocusedIndex\", function MatTabGroup_Template_mat_tab_header_selectFocusedIndex_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.selectedIndex = $event);\n        });\n        i0.ɵɵrepeaterCreate(2, MatTabGroup_For_3_Template, 8, 17, \"div\", 4, i0.ɵɵrepeaterTrackByIdentity);\n        i0.ɵɵelementEnd();\n        i0.ɵɵconditionalCreate(4, MatTabGroup_Conditional_4_Template, 1, 0);\n        i0.ɵɵelementStart(5, \"div\", 5, 1);\n        i0.ɵɵrepeaterCreate(7, MatTabGroup_For_8_Template, 1, 10, \"mat-tab-body\", 6, i0.ɵɵrepeaterTrackByIdentity);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"selectedIndex\", ctx.selectedIndex || 0)(\"disableRipple\", ctx.disableRipple)(\"disablePagination\", ctx.disablePagination)(\"aria-label\", ctx.ariaLabel)(\"aria-labelledby\", ctx.ariaLabelledby);\n        i0.ɵɵadvance(2);\n        i0.ɵɵrepeater(ctx._tabs);\n        i0.ɵɵadvance(2);\n        i0.ɵɵconditional(ctx._isServer ? 4 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationsDisabled());\n        i0.ɵɵadvance(2);\n        i0.ɵɵrepeater(ctx._tabs);\n      }\n    },\n    dependencies: [MatTabHeader, MatTabLabelWrapper, CdkMonitorFocus, MatRipple, CdkPortalOutlet, MatTabBody],\n    styles: [\".mdc-tab{min-width:90px;padding:0 24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;z-index:1}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab--active .mdc-tab__text-label{transition-delay:100ms}._mat-animation-noopable .mdc-tab__text-label{transition:none}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transition:var(--mat-tab-animation-duration, 250ms) transform cubic-bezier(0.4, 0, 0.2, 1);transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}._mat-animation-noopable .mdc-tab-indicator__content,.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mat-mdc-tab-ripple.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;height:var(--mat-tab-container-height, 48px);font-family:var(--mat-tab-label-text-font, var(--mat-sys-title-small-font));font-size:var(--mat-tab-label-text-size, var(--mat-sys-title-small-size));letter-spacing:var(--mat-tab-label-text-tracking, var(--mat-sys-title-small-tracking));line-height:var(--mat-tab-label-text-line-height, var(--mat-sys-title-small-line-height));font-weight:var(--mat-tab-label-text-weight, var(--mat-sys-title-small-weight))}.mat-mdc-tab.mdc-tab{flex-grow:0}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-active-indicator-color, var(--mat-sys-primary));border-top-width:var(--mat-tab-active-indicator-height, 2px);border-radius:var(--mat-tab-active-indicator-shape, 0)}.mat-mdc-tab:hover .mdc-tab__text-label{color:var(--mat-tab-inactive-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab:focus .mdc-tab__text-label{color:var(--mat-tab-inactive-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-active-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-active-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-active-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-active-hover-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-active-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-active-focus-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-disabled-ripple-color, var(--mat-sys-on-surface-variant))}.mat-mdc-tab .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-inactive-label-text-color, var(--mat-sys-on-surface));display:inline-flex;align-items:center}.mat-mdc-tab .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-group.mat-mdc-tab-group-stretch-tabs>.mat-mdc-tab-header .mat-mdc-tab{flex-grow:1}.mat-mdc-tab-group{display:flex;flex-direction:column;max-width:100%}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-background-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-focus-indicator::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-focus-indicator::before{border-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mdc-tab__ripple::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header{flex-direction:column-reverse}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header .mdc-tab-indicator__content--underline{align-self:flex-start}.mat-mdc-tab-body-wrapper{position:relative;overflow:hidden;display:flex;transition:height 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable{transition:none !important;animation:none !important}\\n\"],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabGroup, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tab-group',\n      exportAs: 'matTabGroup',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      providers: [{\n        provide: MAT_TAB_GROUP,\n        useExisting: MatTabGroup\n      }],\n      host: {\n        'class': 'mat-mdc-tab-group',\n        '[class]': '\"mat-\" + (color || \"primary\")',\n        '[class.mat-mdc-tab-group-dynamic-height]': 'dynamicHeight',\n        '[class.mat-mdc-tab-group-inverted-header]': 'headerPosition === \"below\"',\n        '[class.mat-mdc-tab-group-stretch-tabs]': 'stretchTabs',\n        '[attr.mat-align-tabs]': 'alignTabs',\n        '[style.--mat-tab-animation-duration]': 'animationDuration'\n      },\n      imports: [MatTabHeader, MatTabLabelWrapper, CdkMonitorFocus, MatRipple, CdkPortalOutlet, MatTabBody],\n      template: \"<mat-tab-header #tabHeader\\n                [selectedIndex]=\\\"selectedIndex || 0\\\"\\n                [disableRipple]=\\\"disableRipple\\\"\\n                [disablePagination]=\\\"disablePagination\\\"\\n                [aria-label]=\\\"ariaLabel\\\"\\n                [aria-labelledby]=\\\"ariaLabelledby\\\"\\n                (indexFocused)=\\\"_focusChanged($event)\\\"\\n                (selectFocusedIndex)=\\\"selectedIndex = $event\\\">\\n\\n  @for (tab of _tabs; track tab) {\\n    <div class=\\\"mdc-tab mat-mdc-tab mat-focus-indicator\\\"\\n        #tabNode\\n        role=\\\"tab\\\"\\n        matTabLabelWrapper\\n        cdkMonitorElementFocus\\n        [id]=\\\"_getTabLabelId(tab, $index)\\\"\\n        [attr.tabIndex]=\\\"_getTabIndex($index)\\\"\\n        [attr.aria-posinset]=\\\"$index + 1\\\"\\n        [attr.aria-setsize]=\\\"_tabs.length\\\"\\n        [attr.aria-controls]=\\\"_getTabContentId($index)\\\"\\n        [attr.aria-selected]=\\\"selectedIndex === $index\\\"\\n        [attr.aria-label]=\\\"tab.ariaLabel || null\\\"\\n        [attr.aria-labelledby]=\\\"(!tab.ariaLabel && tab.ariaLabelledby) ? tab.ariaLabelledby : null\\\"\\n        [class.mdc-tab--active]=\\\"selectedIndex === $index\\\"\\n        [class]=\\\"tab.labelClass\\\"\\n        [disabled]=\\\"tab.disabled\\\"\\n        [fitInkBarToContent]=\\\"fitInkBarToContent\\\"\\n        (click)=\\\"_handleClick(tab, tabHeader, $index)\\\"\\n        (cdkFocusChange)=\\\"_tabFocusChanged($event, $index)\\\">\\n      <span class=\\\"mdc-tab__ripple\\\"></span>\\n\\n      <!-- Needs to be a separate element, because we can't put\\n          `overflow: hidden` on tab due to the ink bar. -->\\n      <div\\n        class=\\\"mat-mdc-tab-ripple\\\"\\n        mat-ripple\\n        [matRippleTrigger]=\\\"tabNode\\\"\\n        [matRippleDisabled]=\\\"tab.disabled || disableRipple\\\"></div>\\n\\n      <span class=\\\"mdc-tab__content\\\">\\n        <span class=\\\"mdc-tab__text-label\\\">\\n          <!--\\n            If there is a label template, use it, otherwise fall back to the text label.\\n            Note that we don't have indentation around the text label, because it adds\\n            whitespace around the text which breaks some internal tests.\\n          -->\\n          @if (tab.templateLabel) {\\n            <ng-template [cdkPortalOutlet]=\\\"tab.templateLabel\\\"></ng-template>\\n          } @else {{{tab.textLabel}}}\\n        </span>\\n      </span>\\n    </div>\\n  }\\n</mat-tab-header>\\n\\n<!--\\n  We need to project the content somewhere to avoid hydration errors. Some observations:\\n  1. This is only necessary on the server.\\n  2. We get a hydration error if there aren't any nodes after the `ng-content`.\\n  3. We get a hydration error if `ng-content` is wrapped in another element.\\n-->\\n@if (_isServer) {\\n  <ng-content/>\\n}\\n\\n<div\\n  class=\\\"mat-mdc-tab-body-wrapper\\\"\\n  [class._mat-animation-noopable]=\\\"_animationsDisabled()\\\"\\n  #tabBodyWrapper>\\n  @for (tab of _tabs; track tab;) {\\n    <mat-tab-body role=\\\"tabpanel\\\"\\n                 [id]=\\\"_getTabContentId($index)\\\"\\n                 [attr.tabindex]=\\\"(contentTabIndex != null && selectedIndex === $index) ? contentTabIndex : null\\\"\\n                 [attr.aria-labelledby]=\\\"_getTabLabelId(tab, $index)\\\"\\n                 [attr.aria-hidden]=\\\"selectedIndex !== $index\\\"\\n                 [class]=\\\"tab.bodyClass\\\"\\n                 [content]=\\\"tab.content!\\\"\\n                 [position]=\\\"tab.position!\\\"\\n                 [animationDuration]=\\\"animationDuration\\\"\\n                 [preserveContent]=\\\"preserveContent\\\"\\n                 (_onCentered)=\\\"_removeTabBodyWrapperHeight()\\\"\\n                 (_onCentering)=\\\"_setTabBodyWrapperHeight($event)\\\"\\n                 (_beforeCentering)=\\\"_bodyCentered($event)\\\"/>\\n  }\\n</div>\\n\",\n      styles: [\".mdc-tab{min-width:90px;padding:0 24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;z-index:1}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab--active .mdc-tab__text-label{transition-delay:100ms}._mat-animation-noopable .mdc-tab__text-label{transition:none}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transition:var(--mat-tab-animation-duration, 250ms) transform cubic-bezier(0.4, 0, 0.2, 1);transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}._mat-animation-noopable .mdc-tab-indicator__content,.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mat-mdc-tab-ripple.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;height:var(--mat-tab-container-height, 48px);font-family:var(--mat-tab-label-text-font, var(--mat-sys-title-small-font));font-size:var(--mat-tab-label-text-size, var(--mat-sys-title-small-size));letter-spacing:var(--mat-tab-label-text-tracking, var(--mat-sys-title-small-tracking));line-height:var(--mat-tab-label-text-line-height, var(--mat-sys-title-small-line-height));font-weight:var(--mat-tab-label-text-weight, var(--mat-sys-title-small-weight))}.mat-mdc-tab.mdc-tab{flex-grow:0}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-active-indicator-color, var(--mat-sys-primary));border-top-width:var(--mat-tab-active-indicator-height, 2px);border-radius:var(--mat-tab-active-indicator-shape, 0)}.mat-mdc-tab:hover .mdc-tab__text-label{color:var(--mat-tab-inactive-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab:focus .mdc-tab__text-label{color:var(--mat-tab-inactive-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-active-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-active-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-active-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-active-hover-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-active-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-active-focus-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-disabled-ripple-color, var(--mat-sys-on-surface-variant))}.mat-mdc-tab .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-inactive-label-text-color, var(--mat-sys-on-surface));display:inline-flex;align-items:center}.mat-mdc-tab .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-group.mat-mdc-tab-group-stretch-tabs>.mat-mdc-tab-header .mat-mdc-tab{flex-grow:1}.mat-mdc-tab-group{display:flex;flex-direction:column;max-width:100%}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-background-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-focus-indicator::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-focus-indicator::before{border-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mdc-tab__ripple::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header{flex-direction:column-reverse}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header .mdc-tab-indicator__content--underline{align-self:flex-start}.mat-mdc-tab-body-wrapper{position:relative;overflow:hidden;display:flex;transition:height 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable{transition:none !important;animation:none !important}\\n\"]\n    }]\n  }], () => [], {\n    _allTabs: [{\n      type: ContentChildren,\n      args: [MatTab, {\n        descendants: true\n      }]\n    }],\n    _tabBodies: [{\n      type: ViewChildren,\n      args: [MatTabBody]\n    }],\n    _tabBodyWrapper: [{\n      type: ViewChild,\n      args: ['tabBodyWrapper']\n    }],\n    _tabHeader: [{\n      type: ViewChild,\n      args: ['tabHeader']\n    }],\n    color: [{\n      type: Input\n    }],\n    fitInkBarToContent: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    stretchTabs: [{\n      type: Input,\n      args: [{\n        alias: 'mat-stretch-tabs',\n        transform: booleanAttribute\n      }]\n    }],\n    alignTabs: [{\n      type: Input,\n      args: [{\n        alias: 'mat-align-tabs'\n      }]\n    }],\n    dynamicHeight: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    selectedIndex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    headerPosition: [{\n      type: Input\n    }],\n    animationDuration: [{\n      type: Input\n    }],\n    contentTabIndex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    disablePagination: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    preserveContent: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    backgroundColor: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    selectedIndexChange: [{\n      type: Output\n    }],\n    focusChange: [{\n      type: Output\n    }],\n    animationDone: [{\n      type: Output\n    }],\n    selectedTabChange: [{\n      type: Output\n    }]\n  });\n})();\n/** A simple change event emitted on focus or selection changes. */\nclass MatTabChangeEvent {\n  /** Index of the currently-selected tab. */\n  index;\n  /** Reference to the currently-selected tab. */\n  tab;\n}\n\n/**\n * Navigation component matching the styles of the tab group header.\n * Provides anchored navigation with animated ink bar.\n */\nclass MatTabNav extends MatPaginatedTabHeader {\n  _focusedItem = signal(null);\n  /** Whether the ink bar should fit its width to the size of the tab label content. */\n  get fitInkBarToContent() {\n    return this._fitInkBarToContent.value;\n  }\n  set fitInkBarToContent(value) {\n    this._fitInkBarToContent.next(value);\n    this._changeDetectorRef.markForCheck();\n  }\n  _fitInkBarToContent = new BehaviorSubject(false);\n  /** Whether tabs should be stretched to fill the header. */\n  stretchTabs = true;\n  get animationDuration() {\n    return this._animationDuration;\n  }\n  set animationDuration(value) {\n    const stringValue = value + '';\n    this._animationDuration = /^\\d+$/.test(stringValue) ? value + 'ms' : stringValue;\n  }\n  _animationDuration;\n  /** Query list of all tab links of the tab navigation. */\n  _items;\n  /**\n   * Theme color of the background of the tab nav. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/tabs/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  get backgroundColor() {\n    return this._backgroundColor;\n  }\n  set backgroundColor(value) {\n    const classList = this._elementRef.nativeElement.classList;\n    classList.remove('mat-tabs-with-background', `mat-background-${this.backgroundColor}`);\n    if (value) {\n      classList.add('mat-tabs-with-background', `mat-background-${value}`);\n    }\n    this._backgroundColor = value;\n  }\n  _backgroundColor;\n  /** Whether the ripple effect is disabled or not. */\n  get disableRipple() {\n    return this._disableRipple();\n  }\n  set disableRipple(value) {\n    this._disableRipple.set(value);\n  }\n  _disableRipple = signal(false);\n  /**\n   * Theme color of the nav bar. This API is supported in M2 themes only, it has\n   * no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/tabs/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  color = 'primary';\n  /**\n   * Associated tab panel controlled by the nav bar. If not provided, then the nav bar\n   * follows the ARIA link / navigation landmark pattern. If provided, it follows the\n   * ARIA tabs design pattern.\n   */\n  tabPanel;\n  _tabListContainer;\n  _tabList;\n  _tabListInner;\n  _nextPaginator;\n  _previousPaginator;\n  _inkBar;\n  constructor() {\n    const defaultConfig = inject(MAT_TABS_CONFIG, {\n      optional: true\n    });\n    super();\n    this.disablePagination = defaultConfig && defaultConfig.disablePagination != null ? defaultConfig.disablePagination : false;\n    this.fitInkBarToContent = defaultConfig && defaultConfig.fitInkBarToContent != null ? defaultConfig.fitInkBarToContent : false;\n    this.stretchTabs = defaultConfig && defaultConfig.stretchTabs != null ? defaultConfig.stretchTabs : true;\n  }\n  _itemSelected() {\n    // noop\n  }\n  ngAfterContentInit() {\n    this._inkBar = new MatInkBar(this._items);\n    // We need this to run before the `changes` subscription in parent to ensure that the\n    // selectedIndex is up-to-date by the time the super class starts looking for it.\n    this._items.changes.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => this.updateActiveLink());\n    super.ngAfterContentInit();\n    // Turn the `change` stream into a signal to try and avoid \"changed after checked\" errors.\n    this._keyManager.change.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => this._focusedItem.set(this._keyManager?.activeItem || null));\n  }\n  ngAfterViewInit() {\n    if (!this.tabPanel && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw new Error('A mat-tab-nav-panel must be specified via [tabPanel].');\n    }\n    super.ngAfterViewInit();\n  }\n  /** Notifies the component that the active link has been changed. */\n  updateActiveLink() {\n    if (!this._items) {\n      return;\n    }\n    const items = this._items.toArray();\n    for (let i = 0; i < items.length; i++) {\n      if (items[i].active) {\n        this.selectedIndex = i;\n        if (this.tabPanel) {\n          this.tabPanel._activeTabId = items[i].id;\n        }\n        // Updating the `selectedIndex` won't trigger the `change` event on\n        // the key manager so we need to set the signal from here.\n        this._focusedItem.set(items[i]);\n        this._changeDetectorRef.markForCheck();\n        return;\n      }\n    }\n    this.selectedIndex = -1;\n  }\n  _getRole() {\n    return this.tabPanel ? 'tablist' : this._elementRef.nativeElement.getAttribute('role');\n  }\n  _hasFocus(link) {\n    return this._keyManager?.activeItem === link;\n  }\n  static ɵfac = function MatTabNav_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatTabNav)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatTabNav,\n    selectors: [[\"\", \"mat-tab-nav-bar\", \"\"]],\n    contentQueries: function MatTabNav_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatTabLink, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._items = _t);\n      }\n    },\n    viewQuery: function MatTabNav_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c1, 7);\n        i0.ɵɵviewQuery(_c2, 7);\n        i0.ɵɵviewQuery(_c3, 7);\n        i0.ɵɵviewQuery(_c4, 5);\n        i0.ɵɵviewQuery(_c5, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabListContainer = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabList = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabListInner = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._nextPaginator = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._previousPaginator = _t.first);\n      }\n    },\n    hostAttrs: [1, \"mat-mdc-tab-nav-bar\", \"mat-mdc-tab-header\"],\n    hostVars: 17,\n    hostBindings: function MatTabNav_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"role\", ctx._getRole());\n        i0.ɵɵstyleProp(\"--mat-tab-animation-duration\", ctx.animationDuration);\n        i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-controls-enabled\", ctx._showPaginationControls)(\"mat-mdc-tab-header-rtl\", ctx._getLayoutDirection() == \"rtl\")(\"mat-mdc-tab-nav-bar-stretch-tabs\", ctx.stretchTabs)(\"mat-primary\", ctx.color !== \"warn\" && ctx.color !== \"accent\")(\"mat-accent\", ctx.color === \"accent\")(\"mat-warn\", ctx.color === \"warn\")(\"_mat-animation-noopable\", ctx._animationsDisabled);\n      }\n    },\n    inputs: {\n      fitInkBarToContent: [2, \"fitInkBarToContent\", \"fitInkBarToContent\", booleanAttribute],\n      stretchTabs: [2, \"mat-stretch-tabs\", \"stretchTabs\", booleanAttribute],\n      animationDuration: \"animationDuration\",\n      backgroundColor: \"backgroundColor\",\n      disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute],\n      color: \"color\",\n      tabPanel: \"tabPanel\"\n    },\n    exportAs: [\"matTabNavBar\", \"matTabNav\"],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    attrs: _c9,\n    ngContentSelectors: _c0,\n    decls: 13,\n    vars: 6,\n    consts: [[\"previousPaginator\", \"\"], [\"tabListContainer\", \"\"], [\"tabList\", \"\"], [\"tabListInner\", \"\"], [\"nextPaginator\", \"\"], [\"mat-ripple\", \"\", 1, \"mat-mdc-tab-header-pagination\", \"mat-mdc-tab-header-pagination-before\", 3, \"click\", \"mousedown\", \"touchend\", \"matRippleDisabled\"], [1, \"mat-mdc-tab-header-pagination-chevron\"], [1, \"mat-mdc-tab-link-container\", 3, \"keydown\"], [1, \"mat-mdc-tab-list\", 3, \"cdkObserveContent\"], [1, \"mat-mdc-tab-links\"], [\"mat-ripple\", \"\", 1, \"mat-mdc-tab-header-pagination\", \"mat-mdc-tab-header-pagination-after\", 3, \"mousedown\", \"click\", \"touchend\", \"matRippleDisabled\"]],\n    template: function MatTabNav_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 5, 0);\n        i0.ɵɵlistener(\"click\", function MatTabNav_Template_div_click_0_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._handlePaginatorClick(\"before\"));\n        })(\"mousedown\", function MatTabNav_Template_div_mousedown_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._handlePaginatorPress(\"before\", $event));\n        })(\"touchend\", function MatTabNav_Template_div_touchend_0_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._stopInterval());\n        });\n        i0.ɵɵelement(2, \"div\", 6);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"div\", 7, 1);\n        i0.ɵɵlistener(\"keydown\", function MatTabNav_Template_div_keydown_3_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._handleKeydown($event));\n        });\n        i0.ɵɵelementStart(5, \"div\", 8, 2);\n        i0.ɵɵlistener(\"cdkObserveContent\", function MatTabNav_Template_div_cdkObserveContent_5_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._onContentChanges());\n        });\n        i0.ɵɵelementStart(7, \"div\", 9, 3);\n        i0.ɵɵprojection(9);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(10, \"div\", 10, 4);\n        i0.ɵɵlistener(\"mousedown\", function MatTabNav_Template_div_mousedown_10_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._handlePaginatorPress(\"after\", $event));\n        })(\"click\", function MatTabNav_Template_div_click_10_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._handlePaginatorClick(\"after\"));\n        })(\"touchend\", function MatTabNav_Template_div_touchend_10_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._stopInterval());\n        });\n        i0.ɵɵelement(12, \"div\", 6);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-disabled\", ctx._disableScrollBefore);\n        i0.ɵɵproperty(\"matRippleDisabled\", ctx._disableScrollBefore || ctx.disableRipple);\n        i0.ɵɵadvance(10);\n        i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-disabled\", ctx._disableScrollAfter);\n        i0.ɵɵproperty(\"matRippleDisabled\", ctx._disableScrollAfter || ctx.disableRipple);\n      }\n    },\n    dependencies: [MatRipple, CdkObserveContent],\n    styles: [\".mdc-tab{min-width:90px;padding:0 24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;z-index:1}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab--active .mdc-tab__text-label{transition-delay:100ms}._mat-animation-noopable .mdc-tab__text-label{transition:none}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transition:var(--mat-tab-animation-duration, 250ms) transform cubic-bezier(0.4, 0, 0.2, 1);transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}._mat-animation-noopable .mdc-tab-indicator__content,.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mat-mdc-tab-ripple.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;outline:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-pagination-icon-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}.mat-mdc-tab-links{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:flex-end}.cdk-drop-list .mat-mdc-tab-links,.mat-mdc-tab-links.cdk-drop-list{min-height:var(--mat-tab-container-height, 48px)}.mat-mdc-tab-link-container{display:flex;flex-grow:1;overflow:hidden;z-index:1;border-bottom-style:solid;border-bottom-width:var(--mat-tab-divider-height, 1px);border-bottom-color:var(--mat-tab-divider-color, var(--mat-sys-surface-variant))}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-background-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-focus-indicator::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-focus-indicator::before{border-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mdc-tab__ripple::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-foreground-color)}\\n\"],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabNav, [{\n    type: Component,\n    args: [{\n      selector: '[mat-tab-nav-bar]',\n      exportAs: 'matTabNavBar, matTabNav',\n      host: {\n        '[attr.role]': '_getRole()',\n        'class': 'mat-mdc-tab-nav-bar mat-mdc-tab-header',\n        '[class.mat-mdc-tab-header-pagination-controls-enabled]': '_showPaginationControls',\n        '[class.mat-mdc-tab-header-rtl]': \"_getLayoutDirection() == 'rtl'\",\n        '[class.mat-mdc-tab-nav-bar-stretch-tabs]': 'stretchTabs',\n        '[class.mat-primary]': 'color !== \"warn\" && color !== \"accent\"',\n        '[class.mat-accent]': 'color === \"accent\"',\n        '[class.mat-warn]': 'color === \"warn\"',\n        '[class._mat-animation-noopable]': '_animationsDisabled',\n        '[style.--mat-tab-animation-duration]': 'animationDuration'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      imports: [MatRipple, CdkObserveContent],\n      template: \"<!--\\n Note that this intentionally uses a `div` instead of a `button`, because it's not part of\\n the regular tabs flow and is only here to support mouse users. It should also not be focusable.\\n-->\\n<div class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-before\\\"\\n     #previousPaginator\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</div>\\n\\n<div class=\\\"mat-mdc-tab-link-container\\\" #tabListContainer (keydown)=\\\"_handleKeydown($event)\\\">\\n  <div class=\\\"mat-mdc-tab-list\\\" #tabList (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-mdc-tab-links\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</div>\\n\\n<div class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-after\\\"\\n     #nextPaginator\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</div>\\n\",\n      styles: [\".mdc-tab{min-width:90px;padding:0 24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;z-index:1}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab--active .mdc-tab__text-label{transition-delay:100ms}._mat-animation-noopable .mdc-tab__text-label{transition:none}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transition:var(--mat-tab-animation-duration, 250ms) transform cubic-bezier(0.4, 0, 0.2, 1);transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}._mat-animation-noopable .mdc-tab-indicator__content,.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mat-mdc-tab-ripple.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;outline:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-pagination-icon-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}.mat-mdc-tab-links{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:flex-end}.cdk-drop-list .mat-mdc-tab-links,.mat-mdc-tab-links.cdk-drop-list{min-height:var(--mat-tab-container-height, 48px)}.mat-mdc-tab-link-container{display:flex;flex-grow:1;overflow:hidden;z-index:1;border-bottom-style:solid;border-bottom-width:var(--mat-tab-divider-height, 1px);border-bottom-color:var(--mat-tab-divider-color, var(--mat-sys-surface-variant))}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-background-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-focus-indicator::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-focus-indicator::before{border-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mdc-tab__ripple::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-foreground-color)}\\n\"]\n    }]\n  }], () => [], {\n    fitInkBarToContent: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    stretchTabs: [{\n      type: Input,\n      args: [{\n        alias: 'mat-stretch-tabs',\n        transform: booleanAttribute\n      }]\n    }],\n    animationDuration: [{\n      type: Input\n    }],\n    _items: [{\n      type: ContentChildren,\n      args: [forwardRef(() => MatTabLink), {\n        descendants: true\n      }]\n    }],\n    backgroundColor: [{\n      type: Input\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    color: [{\n      type: Input\n    }],\n    tabPanel: [{\n      type: Input\n    }],\n    _tabListContainer: [{\n      type: ViewChild,\n      args: ['tabListContainer', {\n        static: true\n      }]\n    }],\n    _tabList: [{\n      type: ViewChild,\n      args: ['tabList', {\n        static: true\n      }]\n    }],\n    _tabListInner: [{\n      type: ViewChild,\n      args: ['tabListInner', {\n        static: true\n      }]\n    }],\n    _nextPaginator: [{\n      type: ViewChild,\n      args: ['nextPaginator']\n    }],\n    _previousPaginator: [{\n      type: ViewChild,\n      args: ['previousPaginator']\n    }]\n  });\n})();\n/**\n * Link inside a `mat-tab-nav-bar`.\n */\nclass MatTabLink extends InkBarItem {\n  _tabNavBar = inject(MatTabNav);\n  elementRef = inject(ElementRef);\n  _focusMonitor = inject(FocusMonitor);\n  _destroyed = new Subject();\n  /** Whether the tab link is active or not. */\n  _isActive = false;\n  _tabIndex = computed(() => this._tabNavBar._focusedItem() === this ? this.tabIndex : -1);\n  /** Whether the link is active. */\n  get active() {\n    return this._isActive;\n  }\n  set active(value) {\n    if (value !== this._isActive) {\n      this._isActive = value;\n      this._tabNavBar.updateActiveLink();\n    }\n  }\n  /** Whether the tab link is disabled. */\n  disabled = false;\n  /** Whether ripples are disabled on the tab link. */\n  get disableRipple() {\n    return this._disableRipple();\n  }\n  set disableRipple(value) {\n    this._disableRipple.set(value);\n  }\n  _disableRipple = signal(false);\n  tabIndex = 0;\n  /**\n   * Ripple configuration for ripples that are launched on pointer down. The ripple config\n   * is set to the global ripple options since we don't have any configurable options for\n   * the tab link ripples.\n   * @docs-private\n   */\n  rippleConfig;\n  /**\n   * Whether ripples are disabled on interaction.\n   * @docs-private\n   */\n  get rippleDisabled() {\n    return this.disabled || this.disableRipple || this._tabNavBar.disableRipple || !!this.rippleConfig.disabled;\n  }\n  /** Unique id for the tab. */\n  id = inject(_IdGenerator).getId('mat-tab-link-');\n  constructor() {\n    super();\n    inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n    const globalRippleOptions = inject(MAT_RIPPLE_GLOBAL_OPTIONS, {\n      optional: true\n    });\n    const tabIndex = inject(new HostAttributeToken('tabindex'), {\n      optional: true\n    });\n    this.rippleConfig = globalRippleOptions || {};\n    this.tabIndex = tabIndex == null ? 0 : parseInt(tabIndex) || 0;\n    if (_animationsDisabled()) {\n      this.rippleConfig.animation = {\n        enterDuration: 0,\n        exitDuration: 0\n      };\n    }\n    this._tabNavBar._fitInkBarToContent.pipe(takeUntil(this._destroyed)).subscribe(fitInkBarToContent => {\n      this.fitInkBarToContent = fitInkBarToContent;\n    });\n  }\n  /** Focuses the tab link. */\n  focus() {\n    this.elementRef.nativeElement.focus();\n  }\n  ngAfterViewInit() {\n    this._focusMonitor.monitor(this.elementRef);\n  }\n  ngOnDestroy() {\n    this._destroyed.next();\n    this._destroyed.complete();\n    super.ngOnDestroy();\n    this._focusMonitor.stopMonitoring(this.elementRef);\n  }\n  _handleFocus() {\n    // Since we allow navigation through tabbing in the nav bar, we\n    // have to update the focused index whenever the link receives focus.\n    this._tabNavBar.focusIndex = this._tabNavBar._items.toArray().indexOf(this);\n  }\n  _handleKeydown(event) {\n    if (event.keyCode === SPACE || event.keyCode === ENTER) {\n      if (this.disabled) {\n        event.preventDefault();\n      } else if (this._tabNavBar.tabPanel) {\n        // Only prevent the default action on space since it can scroll the page.\n        // Don't prevent enter since it can break link navigation.\n        if (event.keyCode === SPACE) {\n          event.preventDefault();\n        }\n        this.elementRef.nativeElement.click();\n      }\n    }\n  }\n  _getAriaControls() {\n    return this._tabNavBar.tabPanel ? this._tabNavBar.tabPanel?.id : this.elementRef.nativeElement.getAttribute('aria-controls');\n  }\n  _getAriaSelected() {\n    if (this._tabNavBar.tabPanel) {\n      return this.active ? 'true' : 'false';\n    } else {\n      return this.elementRef.nativeElement.getAttribute('aria-selected');\n    }\n  }\n  _getAriaCurrent() {\n    return this.active && !this._tabNavBar.tabPanel ? 'page' : null;\n  }\n  _getRole() {\n    return this._tabNavBar.tabPanel ? 'tab' : this.elementRef.nativeElement.getAttribute('role');\n  }\n  static ɵfac = function MatTabLink_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatTabLink)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatTabLink,\n    selectors: [[\"\", \"mat-tab-link\", \"\"], [\"\", \"matTabLink\", \"\"]],\n    hostAttrs: [1, \"mdc-tab\", \"mat-mdc-tab-link\", \"mat-focus-indicator\"],\n    hostVars: 11,\n    hostBindings: function MatTabLink_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"focus\", function MatTabLink_focus_HostBindingHandler() {\n          return ctx._handleFocus();\n        })(\"keydown\", function MatTabLink_keydown_HostBindingHandler($event) {\n          return ctx._handleKeydown($event);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-controls\", ctx._getAriaControls())(\"aria-current\", ctx._getAriaCurrent())(\"aria-disabled\", ctx.disabled)(\"aria-selected\", ctx._getAriaSelected())(\"id\", ctx.id)(\"tabIndex\", ctx._tabIndex())(\"role\", ctx._getRole());\n        i0.ɵɵclassProp(\"mat-mdc-tab-disabled\", ctx.disabled)(\"mdc-tab--active\", ctx.active);\n      }\n    },\n    inputs: {\n      active: [2, \"active\", \"active\", booleanAttribute],\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n      disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute],\n      tabIndex: [2, \"tabIndex\", \"tabIndex\", value => value == null ? 0 : numberAttribute(value)],\n      id: \"id\"\n    },\n    exportAs: [\"matTabLink\"],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    attrs: _c10,\n    ngContentSelectors: _c0,\n    decls: 5,\n    vars: 2,\n    consts: [[1, \"mdc-tab__ripple\"], [\"mat-ripple\", \"\", 1, \"mat-mdc-tab-ripple\", 3, \"matRippleTrigger\", \"matRippleDisabled\"], [1, \"mdc-tab__content\"], [1, \"mdc-tab__text-label\"]],\n    template: function MatTabLink_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelement(0, \"span\", 0)(1, \"div\", 1);\n        i0.ɵɵelementStart(2, \"span\", 2)(3, \"span\", 3);\n        i0.ɵɵprojection(4);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"matRippleTrigger\", ctx.elementRef.nativeElement)(\"matRippleDisabled\", ctx.rippleDisabled);\n      }\n    },\n    dependencies: [MatRipple],\n    styles: [\".mat-mdc-tab-link{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;height:var(--mat-tab-container-height, 48px);font-family:var(--mat-tab-label-text-font, var(--mat-sys-title-small-font));font-size:var(--mat-tab-label-text-size, var(--mat-sys-title-small-size));letter-spacing:var(--mat-tab-label-text-tracking, var(--mat-sys-title-small-tracking));line-height:var(--mat-tab-label-text-line-height, var(--mat-sys-title-small-line-height));font-weight:var(--mat-tab-label-text-weight, var(--mat-sys-title-small-weight))}.mat-mdc-tab-link.mdc-tab{flex-grow:0}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-active-indicator-color, var(--mat-sys-primary));border-top-width:var(--mat-tab-active-indicator-height, 2px);border-radius:var(--mat-tab-active-indicator-shape, 0)}.mat-mdc-tab-link:hover .mdc-tab__text-label{color:var(--mat-tab-inactive-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link:focus .mdc-tab__text-label{color:var(--mat-tab-inactive-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-active-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab-link.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-active-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-active-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-active-hover-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-active-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-active-focus-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab-link.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab-link.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-disabled-ripple-color, var(--mat-sys-on-surface-variant))}.mat-mdc-tab-link .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-inactive-label-text-color, var(--mat-sys-on-surface));display:inline-flex;align-items:center}.mat-mdc-tab-link .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab-link:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab-link.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab-link.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab-link .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header.mat-mdc-tab-nav-bar-stretch-tabs .mat-mdc-tab-link{flex-grow:1}.mat-mdc-tab-link::before{margin:5px}@media(max-width: 599px){.mat-mdc-tab-link{min-width:72px}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabLink, [{\n    type: Component,\n    args: [{\n      selector: '[mat-tab-link], [matTabLink]',\n      exportAs: 'matTabLink',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'mdc-tab mat-mdc-tab-link mat-focus-indicator',\n        '[attr.aria-controls]': '_getAriaControls()',\n        '[attr.aria-current]': '_getAriaCurrent()',\n        '[attr.aria-disabled]': 'disabled',\n        '[attr.aria-selected]': '_getAriaSelected()',\n        '[attr.id]': 'id',\n        '[attr.tabIndex]': '_tabIndex()',\n        '[attr.role]': '_getRole()',\n        '[class.mat-mdc-tab-disabled]': 'disabled',\n        '[class.mdc-tab--active]': 'active',\n        '(focus)': '_handleFocus()',\n        '(keydown)': '_handleKeydown($event)'\n      },\n      imports: [MatRipple],\n      template: \"<span class=\\\"mdc-tab__ripple\\\"></span>\\n\\n<div\\n  class=\\\"mat-mdc-tab-ripple\\\"\\n  mat-ripple\\n  [matRippleTrigger]=\\\"elementRef.nativeElement\\\"\\n  [matRippleDisabled]=\\\"rippleDisabled\\\"></div>\\n\\n<span class=\\\"mdc-tab__content\\\">\\n  <span class=\\\"mdc-tab__text-label\\\">\\n    <ng-content></ng-content>\\n  </span>\\n</span>\\n\\n\",\n      styles: [\".mat-mdc-tab-link{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;height:var(--mat-tab-container-height, 48px);font-family:var(--mat-tab-label-text-font, var(--mat-sys-title-small-font));font-size:var(--mat-tab-label-text-size, var(--mat-sys-title-small-size));letter-spacing:var(--mat-tab-label-text-tracking, var(--mat-sys-title-small-tracking));line-height:var(--mat-tab-label-text-line-height, var(--mat-sys-title-small-line-height));font-weight:var(--mat-tab-label-text-weight, var(--mat-sys-title-small-weight))}.mat-mdc-tab-link.mdc-tab{flex-grow:0}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-active-indicator-color, var(--mat-sys-primary));border-top-width:var(--mat-tab-active-indicator-height, 2px);border-radius:var(--mat-tab-active-indicator-shape, 0)}.mat-mdc-tab-link:hover .mdc-tab__text-label{color:var(--mat-tab-inactive-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link:focus .mdc-tab__text-label{color:var(--mat-tab-inactive-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-active-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab-link.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-active-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-active-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-active-hover-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-active-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-active-focus-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab-link.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab-link.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-disabled-ripple-color, var(--mat-sys-on-surface-variant))}.mat-mdc-tab-link .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-inactive-label-text-color, var(--mat-sys-on-surface));display:inline-flex;align-items:center}.mat-mdc-tab-link .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab-link:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab-link.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab-link.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab-link .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header.mat-mdc-tab-nav-bar-stretch-tabs .mat-mdc-tab-link{flex-grow:1}.mat-mdc-tab-link::before{margin:5px}@media(max-width: 599px){.mat-mdc-tab-link{min-width:72px}}\\n\"]\n    }]\n  }], () => [], {\n    active: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    tabIndex: [{\n      type: Input,\n      args: [{\n        transform: value => value == null ? 0 : numberAttribute(value)\n      }]\n    }],\n    id: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Tab panel component associated with MatTabNav.\n */\nclass MatTabNavPanel {\n  /** Unique id for the tab panel. */\n  id = inject(_IdGenerator).getId('mat-tab-nav-panel-');\n  /** Id of the active tab in the nav bar. */\n  _activeTabId;\n  static ɵfac = function MatTabNavPanel_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatTabNavPanel)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatTabNavPanel,\n    selectors: [[\"mat-tab-nav-panel\"]],\n    hostAttrs: [\"role\", \"tabpanel\", 1, \"mat-mdc-tab-nav-panel\"],\n    hostVars: 2,\n    hostBindings: function MatTabNavPanel_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-labelledby\", ctx._activeTabId)(\"id\", ctx.id);\n      }\n    },\n    inputs: {\n      id: \"id\"\n    },\n    exportAs: [\"matTabNavPanel\"],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function MatTabNavPanel_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabNavPanel, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tab-nav-panel',\n      exportAs: 'matTabNavPanel',\n      template: '<ng-content></ng-content>',\n      host: {\n        '[attr.aria-labelledby]': '_activeTabId',\n        '[attr.id]': 'id',\n        'class': 'mat-mdc-tab-nav-panel',\n        'role': 'tabpanel'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], null, {\n    id: [{\n      type: Input\n    }]\n  });\n})();\nclass MatTabsModule {\n  static ɵfac = function MatTabsModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatTabsModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatTabsModule,\n    imports: [MatCommonModule, MatTabContent, MatTabLabel, MatTab, MatTabGroup, MatTabNav, MatTabNavPanel, MatTabLink],\n    exports: [MatCommonModule, MatTabContent, MatTabLabel, MatTab, MatTabGroup, MatTabNav, MatTabNavPanel, MatTabLink]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCommonModule, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabsModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatTabContent, MatTabLabel, MatTab, MatTabGroup, MatTabNav, MatTabNavPanel, MatTabLink],\n      exports: [MatCommonModule, MatTabContent, MatTabLabel, MatTab, MatTabGroup, MatTabNav, MatTabNavPanel, MatTabLink]\n    }]\n  }], null, null);\n})();\n\n/**\n * Animations used by the Material tabs.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0.\n */\nconst matTabsAnimations = {\n  // Represents:\n  // trigger('translateTab', [\n  //   // Transitions to `none` instead of 0, because some browsers might blur the content.\n  //   state(\n  //     'center, void, left-origin-center, right-origin-center',\n  //     style({transform: 'none', visibility: 'visible'}),\n  //   ),\n  //   // If the tab is either on the left or right, we additionally add a `min-height` of 1px\n  //   // in order to ensure that the element has a height before its state changes. This is\n  //   // necessary because Chrome does seem to skip the transition in RTL mode if the element does\n  //   // not have a static height and is not rendered. See related issue: #9465\n  //   state(\n  //     'left',\n  //     style({\n  //       transform: 'translate3d(-100%, 0, 0)',\n  //       minHeight: '1px',\n  //       // Normally this is redundant since we detach the content from the DOM, but if the user\n  //       // opted into keeping the content in the DOM, we have to hide it so it isn't focusable.\n  //       visibility: 'hidden',\n  //     }),\n  //   ),\n  //   state(\n  //     'right',\n  //     style({\n  //       transform: 'translate3d(100%, 0, 0)',\n  //       minHeight: '1px',\n  //       visibility: 'hidden',\n  //     }),\n  //   ),\n  //   transition(\n  //     '* => left, * => right, left => center, right => center',\n  //     animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'),\n  //   ),\n  //   transition('void => left-origin-center', [\n  //     style({transform: 'translate3d(-100%, 0, 0)', visibility: 'hidden'}),\n  //     animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'),\n  //   ]),\n  //   transition('void => right-origin-center', [\n  //     style({transform: 'translate3d(100%, 0, 0)', visibility: 'hidden'}),\n  //     animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'),\n  //   ]),\n  // ])\n  /** Animation translates a tab along the X axis. */\n  translateTab: {\n    type: 7,\n    name: 'translateTab',\n    definitions: [{\n      type: 0,\n      name: 'center, void, left-origin-center, right-origin-center',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'none',\n          visibility: 'visible'\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'left',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'translate3d(-100%, 0, 0)',\n          minHeight: '1px',\n          visibility: 'hidden'\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'right',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'translate3d(100%, 0, 0)',\n          minHeight: '1px',\n          visibility: 'hidden'\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: '* => left, * => right, left => center, right => center',\n      animation: {\n        type: 4,\n        styles: null,\n        timings: '{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'\n      },\n      options: null\n    }, {\n      type: 1,\n      expr: 'void => left-origin-center',\n      animation: [{\n        type: 6,\n        styles: {\n          transform: 'translate3d(-100%, 0, 0)',\n          visibility: 'hidden'\n        },\n        offset: null\n      }, {\n        type: 4,\n        styles: null,\n        timings: '{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'\n      }],\n      options: null\n    }, {\n      type: 1,\n      expr: 'void => right-origin-center',\n      animation: [{\n        type: 6,\n        styles: {\n          transform: 'translate3d(100%, 0, 0)',\n          visibility: 'hidden'\n        },\n        offset: null\n      }, {\n        type: 4,\n        styles: null,\n        timings: '{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'\n      }],\n      options: null\n    }],\n    options: {}\n  }\n};\nexport { MAT_TAB, MAT_TABS_CONFIG, MAT_TAB_CONTENT, MAT_TAB_GROUP, MAT_TAB_LABEL, MatInkBar, MatPaginatedTabHeader, MatTab, MatTabBody, MatTabBodyPortal, MatTabChangeEvent, MatTabContent, MatTabGroup, MatTabHeader, MatTabLabel, MatTabLabelWrapper, MatTabLink, MatTabNav, MatTabNavPanel, MatTabsModule, _MAT_INK_BAR_POSITIONER, _MAT_INK_BAR_POSITIONER_FACTORY, matTabsAnimations };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,IAAM,MAAM,CAAC,GAAG;AAChB,SAAS,8BAA8B,IAAI,KAAK;AAC9C,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,CAAC;AAAA,EACnB;AACF;AACA,IAAM,MAAM,CAAC,kBAAkB;AAC/B,IAAM,MAAM,CAAC,SAAS;AACtB,IAAM,MAAM,CAAC,cAAc;AAC3B,IAAM,MAAM,CAAC,eAAe;AAC5B,IAAM,MAAM,CAAC,mBAAmB;AAChC,IAAM,MAAM,CAAC,SAAS;AACtB,SAAS,kCAAkC,IAAI,KAAK;AAAC;AACrD,IAAM,MAAM,CAAC,gBAAgB;AAC7B,IAAM,MAAM,CAAC,WAAW;AACxB,SAAS,uDAAuD,IAAI,KAAK;AAAC;AAC1E,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,wDAAwD,GAAG,GAAG,eAAe,EAAE;AAAA,EAClG;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,EAAE;AAClC,IAAG,WAAW,mBAAmB,OAAO,aAAa;AAAA,EACvD;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AAAA,EACb;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,EAAE;AAClC,IAAG,kBAAkB,OAAO,SAAS;AAAA,EACvC;AACF;AACA,SAAS,2BAA2B,IAAI,KAAK;AAC3C,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,IAAG,WAAW,SAAS,SAAS,kDAAkD;AAChF,YAAM,SAAY,cAAc,GAAG;AACnC,YAAM,SAAS,OAAO;AACtB,YAAM,YAAY,OAAO;AACzB,YAAM,SAAY,cAAc;AAChC,YAAM,eAAkB,YAAY,CAAC;AACrC,aAAU,YAAY,OAAO,aAAa,QAAQ,cAAc,SAAS,CAAC;AAAA,IAC5E,CAAC,EAAE,kBAAkB,SAAS,yDAAyD,QAAQ;AAC7F,YAAM,YAAe,cAAc,GAAG,EAAE;AACxC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,iBAAiB,QAAQ,SAAS,CAAC;AAAA,IAClE,CAAC;AACD,IAAG,UAAU,GAAG,QAAQ,CAAC,EAAE,GAAG,OAAO,CAAC;AACtC,IAAG,eAAe,GAAG,QAAQ,EAAE,EAAE,GAAG,QAAQ,EAAE;AAC9C,IAAG,oBAAoB,GAAG,0CAA0C,GAAG,GAAG,MAAM,EAAE,EAAE,GAAG,0CAA0C,GAAG,CAAC;AACrI,IAAG,aAAa,EAAE,EAAE;AAAA,EACtB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAS,IAAI;AACnB,UAAM,YAAY,IAAI;AACtB,UAAM,aAAgB,YAAY,CAAC;AACnC,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,UAAU;AAC/B,IAAG,YAAY,mBAAmB,OAAO,kBAAkB,SAAS;AACpE,IAAG,WAAW,MAAM,OAAO,eAAe,QAAQ,SAAS,CAAC,EAAE,YAAY,OAAO,QAAQ,EAAE,sBAAsB,OAAO,kBAAkB;AAC1I,IAAG,YAAY,YAAY,OAAO,aAAa,SAAS,CAAC,EAAE,iBAAiB,YAAY,CAAC,EAAE,gBAAgB,OAAO,MAAM,MAAM,EAAE,iBAAiB,OAAO,iBAAiB,SAAS,CAAC,EAAE,iBAAiB,OAAO,kBAAkB,SAAS,EAAE,cAAc,OAAO,aAAa,IAAI,EAAE,mBAAmB,CAAC,OAAO,aAAa,OAAO,iBAAiB,OAAO,iBAAiB,IAAI;AAC9W,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,UAAU,EAAE,qBAAqB,OAAO,YAAY,OAAO,aAAa;AAC1G,IAAG,UAAU,CAAC;AACd,IAAG,cAAc,OAAO,gBAAgB,IAAI,CAAC;AAAA,EAC/C;AACF;AACA,SAAS,mCAAmC,IAAI,KAAK;AACnD,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,CAAC;AAAA,EACnB;AACF;AACA,SAAS,2BAA2B,IAAI,KAAK;AAC3C,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,gBAAgB,EAAE;AACvC,IAAG,WAAW,eAAe,SAAS,iEAAiE;AACrG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,4BAA4B,CAAC;AAAA,IAC5D,CAAC,EAAE,gBAAgB,SAAS,gEAAgE,QAAQ;AAClG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,yBAAyB,MAAM,CAAC;AAAA,IAC/D,CAAC,EAAE,oBAAoB,SAAS,oEAAoE,QAAQ;AAC1G,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,cAAc,MAAM,CAAC;AAAA,IACpD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,UAAM,aAAa,IAAI;AACvB,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,QAAQ,SAAS;AAC/B,IAAG,WAAW,MAAM,OAAO,iBAAiB,UAAU,CAAC,EAAE,WAAW,QAAQ,OAAO,EAAE,YAAY,QAAQ,QAAQ,EAAE,qBAAqB,OAAO,iBAAiB,EAAE,mBAAmB,OAAO,eAAe;AAC3M,IAAG,YAAY,YAAY,OAAO,mBAAmB,QAAQ,OAAO,kBAAkB,aAAa,OAAO,kBAAkB,IAAI,EAAE,mBAAmB,OAAO,eAAe,SAAS,UAAU,CAAC,EAAE,eAAe,OAAO,kBAAkB,UAAU;AAAA,EACrP;AACF;AACA,IAAM,MAAM,CAAC,mBAAmB,EAAE;AAClC,IAAM,OAAO,CAAC,gBAAgB,EAAE;AAChC,IAAM,kBAAkB,IAAI,eAAe,eAAe;AAE1D,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,WAAW,OAAO,WAAW;AAAA,EAC7B,cAAc;AAAA,EAAC;AAAA,EACf,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAAA,IACrC,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,CAAC;AAAA,EACL,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAOH,IAAM,gBAAgB,IAAI,eAAe,aAAa;AAKtD,IAAM,UAAU,IAAI,eAAe,SAAS;AAE5C,IAAM,cAAN,MAAM,qBAAoB,UAAU;AAAA,EAClC,cAAc,OAAO,SAAS;AAAA,IAC5B,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,oBAAoB,mBAAmB;AACrD,cAAQ,6BAA6B,2BAA8B,sBAAsB,YAAW,IAAI,qBAAqB,YAAW;AAAA,IAC1I;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,iBAAiB,EAAE,GAAG,CAAC,IAAI,eAAe,EAAE,CAAC;AAAA,IAC9D,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,gBAAgB,IAAI,eAAe,eAAe;AACxD,IAAM,SAAN,MAAM,QAAO;AAAA,EACX,oBAAoB,OAAO,gBAAgB;AAAA,EAC3C,mBAAmB,OAAO,eAAe;AAAA,IACvC,UAAU;AAAA,EACZ,CAAC;AAAA;AAAA,EAED,WAAW;AAAA;AAAA,EAEX,IAAI,gBAAgB;AAClB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,cAAc,OAAO;AACvB,SAAK,uBAAuB,KAAK;AAAA,EACnC;AAAA,EACA;AAAA;AAAA;AAAA;AAAA,EAIA,mBAAmB;AAAA;AAAA,EAEnB;AAAA;AAAA,EAEA,YAAY;AAAA;AAAA,EAEZ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,KAAK;AAAA;AAAA,EAEL,iBAAiB;AAAA;AAAA,EAEjB,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,gBAAgB,IAAI,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAK5B,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMX,SAAS;AAAA;AAAA;AAAA;AAAA,EAIT,WAAW;AAAA,EACX,cAAc;AACZ,WAAO,sBAAsB,EAAE,KAAK,uBAAuB;AAAA,EAC7D;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,QAAQ,eAAe,WAAW,KAAK,QAAQ,eAAe,UAAU,GAAG;AAC7E,WAAK,cAAc,KAAK;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,SAAS;AAAA,EAC9B;AAAA,EACA,WAAW;AACT,SAAK,iBAAiB,IAAI,eAAe,KAAK,oBAAoB,KAAK,kBAAkB,KAAK,iBAAiB;AAAA,EACjH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,uBAAuB,OAAO;AAK5B,QAAI,SAAS,MAAM,gBAAgB,MAAM;AACvC,WAAK,iBAAiB;AAAA,IACxB;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,eAAe,mBAAmB;AACvD,WAAO,KAAK,qBAAqB,SAAQ;AAAA,EAC3C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,SAAS,CAAC;AAAA,IACvB,gBAAgB,SAAS,sBAAsB,IAAI,KAAK,UAAU;AAChE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,aAAa,CAAC;AAC1C,QAAG,eAAe,UAAU,eAAe,GAAG,WAAW;AAAA,MAC3D;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB,GAAG;AACpE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB,GAAG;AAAA,MACzE;AAAA,IACF;AAAA,IACA,WAAW,SAAS,aAAa,IAAI,KAAK;AACxC,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,aAAa,CAAC;AAAA,MAC/B;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB,GAAG;AAAA,MACzE;AAAA,IACF;AAAA,IACA,WAAW,CAAC,UAAU,EAAE;AAAA,IACxB,UAAU;AAAA,IACV,cAAc,SAAS,oBAAoB,IAAI,KAAK;AAClD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,MAAM,IAAI;AAAA,MAC3B;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,WAAW,CAAC,GAAG,SAAS,WAAW;AAAA,MACnC,WAAW,CAAC,GAAG,cAAc,WAAW;AAAA,MACxC,gBAAgB,CAAC,GAAG,mBAAmB,gBAAgB;AAAA,MACvD,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,IAAI;AAAA,IACN;AAAA,IACA,UAAU,CAAC,QAAQ;AAAA,IACnB,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,oBAAoB;AAAA,IAC5B,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,gBAAgB,IAAI,KAAK;AAC1C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,WAAW,GAAG,+BAA+B,GAAG,GAAG,aAAa;AAAA,MACrE;AAAA,IACF;AAAA,IACA,eAAe;AAAA,EACjB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,QAAQ,CAAC;AAAA,IAC/E,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,MAAM;AAAA;AAAA;AAAA,QAGJ,UAAU;AAAA;AAAA,QAEV,aAAa;AAAA,MACf;AAAA,MACA,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,QACpB,MAAM;AAAA,QACN,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,QAClB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAGH,IAAM,eAAe;AAErB,IAAM,sBAAsB;AAK5B,IAAM,YAAN,MAAgB;AAAA,EACd;AAAA;AAAA,EAEA;AAAA,EACA,YAAY,QAAQ;AAClB,SAAK,SAAS;AAAA,EAChB;AAAA;AAAA,EAEA,OAAO;AACL,SAAK,OAAO,QAAQ,UAAQ,KAAK,iBAAiB,CAAC;AACnD,SAAK,eAAe;AAAA,EACtB;AAAA;AAAA,EAEA,eAAe,SAAS;AACtB,UAAM,oBAAoB,KAAK,OAAO,KAAK,UAAQ,KAAK,WAAW,kBAAkB,OAAO;AAC5F,UAAM,cAAc,KAAK;AACzB,QAAI,sBAAsB,aAAa;AACrC;AAAA,IACF;AACA,iBAAa,iBAAiB;AAC9B,QAAI,mBAAmB;AACrB,YAAM,UAAU,aAAa,WAAW,cAAc,wBAAwB;AAE9E,wBAAkB,eAAe,OAAO;AACxC,WAAK,eAAe;AAAA,IACtB;AAAA,EACF;AACF;AACA,IAAM,aAAN,MAAM,YAAW;AAAA,EACf,cAAc,OAAO,UAAU;AAAA,EAC/B;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA;AAAA,EAEhB,IAAI,qBAAqB;AACvB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,mBAAmB,UAAU;AAC/B,QAAI,KAAK,kBAAkB,UAAU;AACnC,WAAK,gBAAgB;AACrB,UAAI,KAAK,gBAAgB;AACvB,aAAK,qBAAqB;AAAA,MAC5B;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,eAAe,6BAA6B;AAC1C,UAAM,UAAU,KAAK,YAAY;AAGjC,QAAI,CAAC,+BAA+B,CAAC,QAAQ,yBAAyB,CAAC,KAAK,uBAAuB;AACjG,cAAQ,UAAU,IAAI,YAAY;AAClC;AAAA,IACF;AAIA,UAAM,oBAAoB,QAAQ,sBAAsB;AACxD,UAAM,aAAa,4BAA4B,QAAQ,kBAAkB;AACzE,UAAM,YAAY,4BAA4B,OAAO,kBAAkB;AACvE,YAAQ,UAAU,IAAI,mBAAmB;AACzC,SAAK,sBAAsB,MAAM,YAAY,aAAa,cAAc,SAAS,cAAc,UAAU,GAAG;AAE5G,YAAQ,sBAAsB;AAC9B,YAAQ,UAAU,OAAO,mBAAmB;AAC5C,YAAQ,UAAU,IAAI,YAAY;AAClC,SAAK,sBAAsB,MAAM,YAAY,aAAa,EAAE;AAAA,EAC9D;AAAA;AAAA,EAEA,mBAAmB;AACjB,SAAK,YAAY,cAAc,UAAU,OAAO,YAAY;AAAA,EAC9D;AAAA;AAAA,EAEA,WAAW;AACT,SAAK,qBAAqB;AAAA,EAC5B;AAAA;AAAA,EAEA,cAAc;AACZ,SAAK,gBAAgB,OAAO;AAC5B,SAAK,iBAAiB,KAAK,wBAAwB;AAAA,EACrD;AAAA;AAAA,EAEA,uBAAuB;AACrB,UAAM,eAAe,KAAK,YAAY,cAAc,iBAAiB;AACrE,UAAM,gBAAgB,KAAK,iBAAiB,aAAa,cAAc,MAAM;AAC7E,UAAM,uBAAuB,KAAK,wBAAwB,aAAa,cAAc,MAAM;AAC3F,kBAAc,YAAY;AAC1B,yBAAqB,YAAY;AACjC,kBAAc,YAAY,KAAK,qBAAqB;AACpD,SAAK,qBAAqB;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AACrB,QAAI,CAAC,KAAK,mBAAmB,OAAO,cAAc,eAAe,YAAY;AAC3E,YAAM,MAAM,6DAA6D;AAAA,IAC3E;AACA,UAAM,gBAAgB,KAAK,gBAAgB,KAAK,YAAY,cAAc,cAAc,mBAAmB,IAAI,KAAK,YAAY;AAChI,QAAI,CAAC,kBAAkB,OAAO,cAAc,eAAe,YAAY;AACrE,YAAM,MAAM,qCAAqC;AAAA,IACnD;AACA,kBAAc,YAAY,KAAK,cAAc;AAAA,EAC/C;AAAA,EACA,OAAO,OAAO,SAAS,mBAAmB,mBAAmB;AAC3D,WAAO,KAAK,qBAAqB,aAAY;AAAA,EAC/C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,oBAAoB,CAAC,GAAG,sBAAsB,sBAAsB,gBAAgB;AAAA,IACtF;AAAA,EACF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM;AAAA,IACR,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,SAAS,kCAAkC;AACzC,QAAM,SAAS,cAAY;AAAA,IACzB,MAAM,WAAW,QAAQ,cAAc,KAAK,OAAO;AAAA,IACnD,OAAO,WAAW,QAAQ,eAAe,KAAK,OAAO;AAAA,EACvD;AACA,SAAO;AACT;AAEA,IAAM,0BAA0B,IAAI,eAAe,uBAAuB;AAAA,EACxE,YAAY;AAAA,EACZ,SAAS;AACX,CAAC;AAMD,IAAM,qBAAN,MAAM,4BAA2B,WAAW;AAAA,EAC1C,aAAa,OAAO,UAAU;AAAA;AAAA,EAE9B,WAAW;AAAA;AAAA,EAEX,QAAQ;AACN,SAAK,WAAW,cAAc,MAAM;AAAA,EACtC;AAAA,EACA,gBAAgB;AACd,WAAO,KAAK,WAAW,cAAc;AAAA,EACvC;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK,WAAW,cAAc;AAAA,EACvC;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,2BAA2B,mBAAmB;AAC5D,cAAQ,oCAAoC,kCAAqC,sBAAsB,mBAAkB,IAAI,qBAAqB,mBAAkB;AAAA,IACtK;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,sBAAsB,EAAE,CAAC;AAAA,IAC1C,UAAU;AAAA,IACV,cAAc,SAAS,gCAAgC,IAAI,KAAK;AAC9D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,iBAAiB,CAAC,CAAC,IAAI,QAAQ;AAC9C,QAAG,YAAY,wBAAwB,IAAI,QAAQ;AAAA,MACrD;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,IACxD;AAAA,IACA,UAAU,CAAI,0BAA0B;AAAA,EAC1C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,gCAAgC;AAAA,QAChC,wBAAwB;AAAA,MAC1B;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAGH,IAAM,8BAA8B;AAAA,EAClC,SAAS;AACX;AAKA,IAAM,sBAAsB;AAK5B,IAAM,yBAAyB;AAK/B,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,cAAc,OAAO,UAAU;AAAA,EAC/B,qBAAqB,OAAO,iBAAiB;AAAA,EAC7C,iBAAiB,OAAO,aAAa;AAAA,EACrC,OAAO,OAAO,gBAAgB;AAAA,IAC5B,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,UAAU,OAAO,MAAM;AAAA,EACvB,YAAY,OAAO,QAAQ;AAAA,EAC3B,wBAAwB,OAAO,oBAAoB;AAAA,EACnD,YAAY,OAAO,QAAQ;AAAA,EAC3B,YAAY,OAAO,SAAS;AAAA,EAC5B,sBAAsB,oBAAoB;AAAA,EAC1C;AAAA;AAAA,EAEA,kBAAkB;AAAA;AAAA,EAElB,wBAAwB;AAAA;AAAA,EAExB,aAAa,IAAI,QAAQ;AAAA;AAAA,EAEzB,0BAA0B;AAAA;AAAA,EAE1B,sBAAsB;AAAA;AAAA,EAEtB,uBAAuB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvB;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,iBAAiB,IAAI,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAK7B,oBAAoB;AAAA;AAAA,EAEpB,IAAI,gBAAgB;AAClB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,cAAc,GAAG;AACnB,UAAM,QAAQ,MAAM,CAAC,IAAI,IAAI;AAC7B,QAAI,KAAK,kBAAkB,OAAO;AAChC,WAAK,wBAAwB;AAC7B,WAAK,iBAAiB;AACtB,UAAI,KAAK,aAAa;AACpB,aAAK,YAAY,iBAAiB,KAAK;AAAA,MACzC;AAAA,IACF;AAAA,EACF;AAAA,EACA,iBAAiB;AAAA;AAAA,EAEjB,qBAAqB,IAAI,aAAa;AAAA;AAAA,EAEtC,eAAe,IAAI,aAAa;AAAA,EAChC,cAAc;AAEZ,SAAK,iBAAiB,KAAK,QAAQ,kBAAkB,MAAM,CAAC,KAAK,UAAU,OAAO,KAAK,YAAY,eAAe,cAAc,MAAM,KAAK,cAAc,CAAC,CAAC,CAAC;AAAA,EAC9J;AAAA,EACA,kBAAkB;AAEhB,SAAK,eAAe,KAAK,KAAK,UAAU,OAAO,KAAK,mBAAmB,eAAe,cAAc,MAAM,KAAK,sBAAsB,QAAQ,GAAG,2BAA2B,GAAG,KAAK,UAAU,OAAO,KAAK,eAAe,eAAe,cAAc,MAAM,KAAK,sBAAsB,OAAO,GAAG,2BAA2B,CAAC;AAAA,EAC9T;AAAA,EACA,qBAAqB;AACnB,UAAM,YAAY,KAAK,OAAO,KAAK,KAAK,SAAS,GAAG,KAAK;AAKzD,UAAM,SAAS,KAAK,sBAAsB,QAAQ,KAAK,YAAY,aAAa,EAAE,KAAK,aAAa,EAAE,GAAG,UAAU,KAAK,UAAU,CAAC;AAInI,UAAM,iBAAiB,KAAK,eAAe,OAAO,GAAG,EAAE,KAAK,UAAU,KAAK,UAAU,CAAC;AACtF,UAAM,UAAU,MAAM;AACpB,WAAK,iBAAiB;AACtB,WAAK,0BAA0B;AAAA,IACjC;AACA,SAAK,cAAc,IAAI,gBAAgB,KAAK,MAAM,EAAE,0BAA0B,KAAK,oBAAoB,CAAC,EAAE,eAAe,EAAE,SAAS,EAEnI,cAAc,MAAM,KAAK;AAG1B,SAAK,YAAY,iBAAiB,KAAK,IAAI,KAAK,gBAAgB,CAAC,CAAC;AAIlE,oBAAgB,SAAS;AAAA,MACvB,UAAU,KAAK;AAAA,IACjB,CAAC;AAGD,UAAM,WAAW,gBAAgB,QAAQ,KAAK,OAAO,SAAS,KAAK,cAAc,CAAC,EAAE,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM;AAInI,WAAK,QAAQ,IAAI,MAAM;AACrB,gBAAQ,QAAQ,EAAE,KAAK,MAAM;AAE3B,eAAK,kBAAkB,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,sBAAsB,GAAG,KAAK,eAAe,CAAC;AAC/F,kBAAQ;AAAA,QACV,CAAC;AAAA,MACH,CAAC;AACD,WAAK,aAAa,0BAA0B,KAAK,oBAAoB,CAAC;AAAA,IACxE,CAAC;AAID,SAAK,YAAY,OAAO,UAAU,mBAAiB;AACjD,WAAK,aAAa,KAAK,aAAa;AACpC,WAAK,aAAa,aAAa;AAAA,IACjC,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,gBAAgB;AACd,QAAI,OAAO,mBAAmB,YAAY;AACxC,aAAO;AAAA,IACT;AACA,WAAO,KAAK,OAAO,QAAQ;AAAA,MAAK,UAAU,KAAK,MAAM;AAAA,MAAG,UAAU,cAAY,IAAI,WAAW,cAAY,KAAK,QAAQ,kBAAkB,MAAM;AAC5I,cAAM,iBAAiB,IAAI,eAAe,aAAW,SAAS,KAAK,OAAO,CAAC;AAC3E,iBAAS,QAAQ,UAAQ,eAAe,QAAQ,KAAK,WAAW,aAAa,CAAC;AAC9E,eAAO,MAAM;AACX,yBAAe,WAAW;AAAA,QAC5B;AAAA,MACF,CAAC,CAAC,CAAC;AAAA;AAAA;AAAA,MAGH,KAAK,CAAC;AAAA;AAAA;AAAA,MAGN,OAAO,aAAW,QAAQ,KAAK,OAAK,EAAE,YAAY,QAAQ,KAAK,EAAE,YAAY,SAAS,CAAC,CAAC;AAAA,IAAC;AAAA,EAC3F;AAAA,EACA,wBAAwB;AAEtB,QAAI,KAAK,kBAAkB,KAAK,OAAO,QAAQ;AAC7C,WAAK,iBAAiB;AACtB,WAAK,iBAAiB,KAAK,OAAO;AAClC,WAAK,mBAAmB,aAAa;AAAA,IACvC;AAGA,QAAI,KAAK,uBAAuB;AAC9B,WAAK,eAAe,KAAK,cAAc;AACvC,WAAK,wBAAwB;AAC7B,WAAK,0BAA0B;AAC/B,WAAK,wBAAwB;AAC7B,WAAK,mBAAmB,aAAa;AAAA,IACvC;AAGA,QAAI,KAAK,wBAAwB;AAC/B,WAAK,yBAAyB;AAC9B,WAAK,yBAAyB;AAC9B,WAAK,mBAAmB,aAAa;AAAA,IACvC;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,eAAe,QAAQ,aAAW,QAAQ,CAAC;AAChD,SAAK,aAAa,QAAQ;AAC1B,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AACzB,SAAK,eAAe,SAAS;AAAA,EAC/B;AAAA;AAAA,EAEA,eAAe,OAAO;AAEpB,QAAI,eAAe,KAAK,GAAG;AACzB;AAAA,IACF;AACA,YAAQ,MAAM,SAAS;AAAA,MACrB,KAAK;AAAA,MACL,KAAK;AACH,YAAI,KAAK,eAAe,KAAK,eAAe;AAC1C,gBAAM,OAAO,KAAK,OAAO,IAAI,KAAK,UAAU;AAC5C,cAAI,QAAQ,CAAC,KAAK,UAAU;AAC1B,iBAAK,mBAAmB,KAAK,KAAK,UAAU;AAC5C,iBAAK,cAAc,KAAK;AAAA,UAC1B;AAAA,QACF;AACA;AAAA,MACF;AACE,aAAK,aAAa,UAAU,KAAK;AAAA,IACrC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,oBAAoB;AAClB,UAAM,cAAc,KAAK,YAAY,cAAc;AAInD,QAAI,gBAAgB,KAAK,qBAAqB;AAC5C,WAAK,sBAAsB,eAAe;AAG1C,WAAK,QAAQ,IAAI,MAAM;AACrB,aAAK,iBAAiB;AACtB,aAAK,0BAA0B;AAC/B,aAAK,mBAAmB,aAAa;AAAA,MACvC,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,mBAAmB;AACjB,SAAK,wBAAwB;AAC7B,SAAK,wBAAwB;AAC7B,SAAK,yBAAyB;AAAA,EAChC;AAAA;AAAA,EAEA,IAAI,aAAa;AACf,WAAO,KAAK,cAAc,KAAK,YAAY,kBAAkB;AAAA,EAC/D;AAAA;AAAA,EAEA,IAAI,WAAW,OAAO;AACpB,QAAI,CAAC,KAAK,cAAc,KAAK,KAAK,KAAK,eAAe,SAAS,CAAC,KAAK,aAAa;AAChF;AAAA,IACF;AACA,SAAK,YAAY,cAAc,KAAK;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,OAAO;AACnB,WAAO,KAAK,SAAS,CAAC,CAAC,KAAK,OAAO,QAAQ,EAAE,KAAK,IAAI;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,UAAU;AACrB,QAAI,KAAK,yBAAyB;AAChC,WAAK,eAAe,QAAQ;AAAA,IAC9B;AACA,QAAI,KAAK,UAAU,KAAK,OAAO,QAAQ;AACrC,WAAK,OAAO,QAAQ,EAAE,QAAQ,EAAE,MAAM;AAItC,YAAM,cAAc,KAAK,kBAAkB;AAC3C,YAAM,MAAM,KAAK,oBAAoB;AACrC,UAAI,OAAO,OAAO;AAChB,oBAAY,aAAa;AAAA,MAC3B,OAAO;AACL,oBAAY,aAAa,YAAY,cAAc,YAAY;AAAA,MACjE;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,sBAAsB;AACpB,WAAO,KAAK,QAAQ,KAAK,KAAK,UAAU,QAAQ,QAAQ;AAAA,EAC1D;AAAA;AAAA,EAEA,2BAA2B;AACzB,QAAI,KAAK,mBAAmB;AAC1B;AAAA,IACF;AACA,UAAM,iBAAiB,KAAK;AAC5B,UAAM,aAAa,KAAK,oBAAoB,MAAM,QAAQ,CAAC,iBAAiB;AAO5E,SAAK,SAAS,cAAc,MAAM,YAAY,cAAc,KAAK,MAAM,UAAU,CAAC;AAKlF,QAAI,KAAK,UAAU,WAAW,KAAK,UAAU,MAAM;AACjD,WAAK,kBAAkB,cAAc,aAAa;AAAA,IACpD;AAAA,EACF;AAAA;AAAA,EAEA,IAAI,iBAAiB;AACnB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,eAAe,OAAO;AACxB,SAAK,UAAU,KAAK;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,cAAc,WAAW;AACvB,UAAM,aAAa,KAAK,kBAAkB,cAAc;AAExD,UAAM,gBAAgB,aAAa,WAAW,KAAK,KAAK,aAAa;AACrE,WAAO,KAAK,UAAU,KAAK,kBAAkB,YAAY;AAAA,EAC3D;AAAA;AAAA,EAEA,sBAAsB,WAAW;AAC/B,SAAK,cAAc;AACnB,SAAK,cAAc,SAAS;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe,YAAY;AACzB,QAAI,KAAK,mBAAmB;AAC1B;AAAA,IACF;AACA,UAAM,gBAAgB,KAAK,SAAS,KAAK,OAAO,QAAQ,EAAE,UAAU,IAAI;AACxE,QAAI,CAAC,eAAe;AAClB;AAAA,IACF;AAEA,UAAM,aAAa,KAAK,kBAAkB,cAAc;AACxD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,WAAW;AAC7B,QAAI,gBAAgB;AACpB,QAAI,KAAK,oBAAoB,KAAK,OAAO;AACvC,uBAAiB;AACjB,sBAAgB,iBAAiB;AAAA,IACnC,OAAO;AACL,sBAAgB,KAAK,cAAc,cAAc,cAAc;AAC/D,uBAAiB,gBAAgB;AAAA,IACnC;AACA,UAAM,mBAAmB,KAAK;AAC9B,UAAM,kBAAkB,KAAK,iBAAiB;AAC9C,QAAI,iBAAiB,kBAAkB;AAErC,WAAK,kBAAkB,mBAAmB;AAAA,IAC5C,WAAW,gBAAgB,iBAAiB;AAE1C,WAAK,kBAAkB,KAAK,IAAI,gBAAgB,iBAAiB,iBAAiB,gBAAgB;AAAA,IACpG;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,0BAA0B;AACxB,QAAI,KAAK,mBAAmB;AAC1B,WAAK,0BAA0B;AAAA,IACjC,OAAO;AACL,YAAM,cAAc,KAAK,cAAc,cAAc;AACrD,YAAM,iBAAiB,KAAK,YAAY,cAAc;AAStD,YAAM,YAAY,cAAc,kBAAkB;AAClD,UAAI,CAAC,WAAW;AACd,aAAK,iBAAiB;AAAA,MACxB;AACA,UAAI,cAAc,KAAK,yBAAyB;AAC9C,aAAK,0BAA0B;AAC/B,aAAK,mBAAmB,aAAa;AAAA,MACvC;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,0BAA0B;AACxB,QAAI,KAAK,mBAAmB;AAC1B,WAAK,sBAAsB,KAAK,uBAAuB;AAAA,IACzD,OAAO;AAEL,WAAK,uBAAuB,KAAK,kBAAkB;AACnD,WAAK,sBAAsB,KAAK,kBAAkB,KAAK,sBAAsB;AAC7E,WAAK,mBAAmB,aAAa;AAAA,IACvC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,wBAAwB;AACtB,UAAM,kBAAkB,KAAK,cAAc,cAAc;AACzD,UAAM,aAAa,KAAK,kBAAkB,cAAc;AACxD,WAAO,kBAAkB,cAAc;AAAA,EACzC;AAAA;AAAA,EAEA,4BAA4B;AAC1B,UAAM,eAAe,KAAK,UAAU,KAAK,OAAO,SAAS,KAAK,OAAO,QAAQ,EAAE,KAAK,aAAa,IAAI;AACrG,UAAM,uBAAuB,eAAe,aAAa,WAAW,gBAAgB;AACpF,QAAI,sBAAsB;AACxB,WAAK,QAAQ,eAAe,oBAAoB;AAAA,IAClD,OAAO;AACL,WAAK,QAAQ,KAAK;AAAA,IACpB;AAAA,EACF;AAAA;AAAA,EAEA,gBAAgB;AACd,SAAK,eAAe,KAAK;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB,WAAW,YAAY;AAG3C,QAAI,cAAc,WAAW,UAAU,QAAQ,WAAW,WAAW,GAAG;AACtE;AAAA,IACF;AAEA,SAAK,cAAc;AAEnB,UAAM,qBAAqB,sBAAsB,EAEhD,KAAK,UAAU,MAAM,KAAK,gBAAgB,KAAK,UAAU,CAAC,CAAC,EAAE,UAAU,MAAM;AAC5E,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,KAAK,cAAc,SAAS;AAEhC,UAAI,aAAa,KAAK,YAAY,mBAAmB;AACnD,aAAK,cAAc;AAAA,MACrB;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,UAAU;AAClB,QAAI,KAAK,mBAAmB;AAC1B,aAAO;AAAA,QACL,mBAAmB;AAAA,QACnB,UAAU;AAAA,MACZ;AAAA,IACF;AACA,UAAM,oBAAoB,KAAK,sBAAsB;AACrD,SAAK,kBAAkB,KAAK,IAAI,GAAG,KAAK,IAAI,mBAAmB,QAAQ,CAAC;AAGxE,SAAK,yBAAyB;AAC9B,SAAK,wBAAwB;AAC7B,WAAO;AAAA,MACL;AAAA,MACA,UAAU,KAAK;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,8BAA8B,mBAAmB;AACtE,WAAO,KAAK,qBAAqB,wBAAuB;AAAA,EAC1D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,mBAAmB,CAAC,GAAG,qBAAqB,qBAAqB,gBAAgB;AAAA,MACjF,eAAe,CAAC,GAAG,iBAAiB,iBAAiB,eAAe;AAAA,IACtE;AAAA,IACA,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,cAAc;AAAA,IAChB;AAAA,EACF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AASH,IAAM,eAAN,MAAM,sBAAqB,sBAAsB;AAAA,EAC/C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,gBAAgB;AAAA,EAChB,qBAAqB;AACnB,SAAK,UAAU,IAAI,UAAU,KAAK,MAAM;AACxC,UAAM,mBAAmB;AAAA,EAC3B;AAAA,EACA,cAAc,OAAO;AACnB,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,qBAAqB,mBAAmB;AACtD,cAAQ,8BAA8B,4BAA+B,sBAAsB,aAAY,IAAI,qBAAqB,aAAY;AAAA,IAC9I;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,gBAAgB,CAAC;AAAA,IAC9B,gBAAgB,SAAS,4BAA4B,IAAI,KAAK,UAAU;AACtE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,oBAAoB,CAAC;AAAA,MACnD;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,SAAS;AAAA,MAC5D;AAAA,IACF;AAAA,IACA,WAAW,SAAS,mBAAmB,IAAI,KAAK;AAC9C,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AACrB,QAAG,YAAY,KAAK,CAAC;AACrB,QAAG,YAAY,KAAK,CAAC;AACrB,QAAG,YAAY,KAAK,CAAC;AACrB,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,oBAAoB,GAAG;AACxE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,WAAW,GAAG;AAC/D,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB,GAAG;AACpE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,iBAAiB,GAAG;AACrE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,qBAAqB,GAAG;AAAA,MAC3E;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,oBAAoB;AAAA,IACnC,UAAU;AAAA,IACV,cAAc,SAAS,0BAA0B,IAAI,KAAK;AACxD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,kDAAkD,IAAI,uBAAuB,EAAE,0BAA0B,IAAI,oBAAoB,KAAK,KAAK;AAAA,MAC5J;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,WAAW,CAAC,GAAG,cAAc,WAAW;AAAA,MACxC,gBAAgB,CAAC,GAAG,mBAAmB,gBAAgB;AAAA,MACvD,eAAe,CAAC,GAAG,iBAAiB,iBAAiB,gBAAgB;AAAA,IACvE;AAAA,IACA,UAAU,CAAI,0BAA0B;AAAA,IACxC,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,qBAAqB,EAAE,GAAG,CAAC,oBAAoB,EAAE,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,gBAAgB,EAAE,GAAG,CAAC,iBAAiB,EAAE,GAAG,CAAC,cAAc,IAAI,GAAG,iCAAiC,wCAAwC,GAAG,SAAS,aAAa,YAAY,mBAAmB,GAAG,CAAC,GAAG,uCAAuC,GAAG,CAAC,GAAG,+BAA+B,GAAG,SAAS,GAAG,CAAC,QAAQ,WAAW,GAAG,oBAAoB,GAAG,mBAAmB,GAAG,CAAC,GAAG,oBAAoB,GAAG,CAAC,cAAc,IAAI,GAAG,iCAAiC,uCAAuC,GAAG,aAAa,SAAS,YAAY,mBAAmB,CAAC;AAAA,IAC5mB,UAAU,SAAS,sBAAsB,IAAI,KAAK;AAChD,UAAI,KAAK,GAAG;AACV,cAAM,MAAS,iBAAiB;AAChC,QAAG,gBAAgB;AACnB,QAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,QAAG,WAAW,SAAS,SAAS,6CAA6C;AAC3E,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,sBAAsB,QAAQ,CAAC;AAAA,QAC3D,CAAC,EAAE,aAAa,SAAS,+CAA+C,QAAQ;AAC9E,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,sBAAsB,UAAU,MAAM,CAAC;AAAA,QACnE,CAAC,EAAE,YAAY,SAAS,gDAAgD;AACtE,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,cAAc,CAAC;AAAA,QAC3C,CAAC;AACD,QAAG,UAAU,GAAG,OAAO,CAAC;AACxB,QAAG,aAAa;AAChB,QAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,QAAG,WAAW,WAAW,SAAS,6CAA6C,QAAQ;AACrF,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,eAAe,MAAM,CAAC;AAAA,QAClD,CAAC;AACD,QAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,QAAG,WAAW,qBAAqB,SAAS,yDAAyD;AACnG,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,kBAAkB,CAAC;AAAA,QAC/C,CAAC;AACD,QAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,QAAG,aAAa,CAAC;AACjB,QAAG,aAAa,EAAE,EAAE;AACpB,QAAG,eAAe,IAAI,OAAO,IAAI,CAAC;AAClC,QAAG,WAAW,aAAa,SAAS,gDAAgD,QAAQ;AAC1F,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,sBAAsB,SAAS,MAAM,CAAC;AAAA,QAClE,CAAC,EAAE,SAAS,SAAS,8CAA8C;AACjE,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,sBAAsB,OAAO,CAAC;AAAA,QAC1D,CAAC,EAAE,YAAY,SAAS,iDAAiD;AACvE,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,cAAc,CAAC;AAAA,QAC3C,CAAC;AACD,QAAG,UAAU,IAAI,OAAO,CAAC;AACzB,QAAG,aAAa;AAAA,MAClB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,0CAA0C,IAAI,oBAAoB;AACjF,QAAG,WAAW,qBAAqB,IAAI,wBAAwB,IAAI,aAAa;AAChF,QAAG,UAAU,CAAC;AACd,QAAG,YAAY,2BAA2B,IAAI,mBAAmB;AACjE,QAAG,UAAU,CAAC;AACd,QAAG,YAAY,cAAc,IAAI,aAAa,IAAI,EAAE,mBAAmB,IAAI,kBAAkB,IAAI;AACjG,QAAG,UAAU,CAAC;AACd,QAAG,YAAY,0CAA0C,IAAI,mBAAmB;AAChF,QAAG,WAAW,qBAAqB,IAAI,uBAAuB,IAAI,aAAa;AAAA,MACjF;AAAA,IACF;AAAA,IACA,cAAc,CAAC,WAAW,iBAAiB;AAAA,IAC3C,QAAQ,CAAC,yyFAAyyF;AAAA,IAClzF,eAAe;AAAA,EACjB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,0DAA0D;AAAA,QAC1D,kCAAkC;AAAA,MACpC;AAAA,MACA,SAAS,CAAC,WAAW,iBAAiB;AAAA,MACtC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,yyFAAyyF;AAAA,IACpzF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,QACzB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,QACzB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,QAChB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,QACrB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAGH,IAAM,kBAAkB,IAAI,eAAe,iBAAiB;AAM5D,IAAM,mBAAN,MAAM,0BAAyB,gBAAgB;AAAA,EAC7C,QAAQ,OAAO,UAAU;AAAA;AAAA,EAEzB,gBAAgB,aAAa;AAAA;AAAA,EAE7B,cAAc,aAAa;AAAA,EAC3B,cAAc;AACZ,UAAM;AAAA,EACR;AAAA;AAAA,EAEA,WAAW;AACT,UAAM,SAAS;AACf,SAAK,gBAAgB,KAAK,MAAM,iBAAiB,KAAK,UAAU,KAAK,MAAM,kBAAkB,CAAC,CAAC,EAAE,UAAU,iBAAe;AACxH,UAAI,KAAK,MAAM,YAAY,eAAe,CAAC,KAAK,YAAY,GAAG;AAC7D,aAAK,OAAO,KAAK,MAAM,QAAQ;AAAA,MACjC;AAAA,IACF,CAAC;AACD,SAAK,cAAc,KAAK,MAAM,oBAAoB,UAAU,MAAM;AAChE,UAAI,CAAC,KAAK,MAAM,iBAAiB;AAC/B,aAAK,OAAO;AAAA,MACd;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,cAAc;AACZ,UAAM,YAAY;AAClB,SAAK,cAAc,YAAY;AAC/B,SAAK,YAAY,YAAY;AAAA,EAC/B;AAAA,EACA,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,kBAAkB,EAAE,CAAC;AAAA,IACtC,UAAU,CAAI,0BAA0B;AAAA,EAC1C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAKH,IAAM,aAAN,MAAM,YAAW;AAAA,EACf,cAAc,OAAO,UAAU;AAAA,EAC/B,OAAO,OAAO,gBAAgB;AAAA,IAC5B,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,UAAU,OAAO,MAAM;AAAA,EACvB,YAAY,OAAO,QAAQ;AAAA,EAC3B,YAAY,OAAO,SAAS;AAAA,EAC5B,wBAAwB,oBAAoB;AAAA,EAC5C;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,yBAAyB,aAAa;AAAA;AAAA,EAEtC;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,eAAe,IAAI,aAAa;AAAA;AAAA,EAEhC,mBAAmB,IAAI,aAAa;AAAA;AAAA,EAEpC,sBAAsB,IAAI,aAAa;AAAA;AAAA,EAEvC,cAAc,IAAI,aAAa,IAAI;AAAA;AAAA,EAEnC;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA,EAIA,oBAAoB;AAAA;AAAA,EAEpB,kBAAkB;AAAA;AAAA,EAElB,IAAI,SAAS,UAAU;AACrB,SAAK,iBAAiB;AACtB,SAAK,+BAA+B;AAAA,EACtC;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,MAAM;AACb,YAAM,oBAAoB,OAAO,iBAAiB;AAClD,WAAK,yBAAyB,KAAK,KAAK,OAAO,UAAU,SAAO;AAC9D,aAAK,+BAA+B,GAAG;AACvC,0BAAkB,aAAa;AAAA,MACjC,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,WAAW;AACT,SAAK,sBAAsB;AAC3B,QAAI,KAAK,cAAc,UAAU;AAC/B,WAAK,gBAAgB,IAAI;AAEzB,sBAAgB,MAAM,KAAK,aAAa,KAAK,KAAK,YAAY,cAAc,YAAY,GAAG;AAAA,QACzF,UAAU,KAAK;AAAA,MACjB,CAAC;AAAA,IACH;AACA,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,cAAc;AACZ,iBAAa,KAAK,cAAc;AAChC,SAAK,gBAAgB,QAAQ,aAAW,QAAQ,CAAC;AACjD,SAAK,uBAAuB,YAAY;AAAA,EAC1C;AAAA;AAAA,EAEA,wBAAwB;AACtB,SAAK,QAAQ,kBAAkB,MAAM;AACnC,YAAM,UAAU,KAAK,YAAY;AACjC,YAAM,iBAAiB,WAAS;AAC9B,YAAI,MAAM,WAAW,KAAK,iBAAiB,eAAe;AACxD,eAAK,YAAY,cAAc,UAAU,OAAO,wBAAwB;AAGxE,cAAI,MAAM,SAAS,iBAAiB;AAClC,iBAAK,gBAAgB;AAAA,UACvB;AAAA,QACF;AAAA,MACF;AACA,WAAK,iBAAiB,CAAC,KAAK,UAAU,OAAO,SAAS,mBAAmB,WAAS;AAChF,YAAI,MAAM,WAAW,KAAK,iBAAiB,eAAe;AACxD,eAAK,YAAY,cAAc,UAAU,IAAI,wBAAwB;AACrE,eAAK,mBAAmB;AAAA,QAC1B;AAAA,MACF,CAAC,GAAG,KAAK,UAAU,OAAO,SAAS,iBAAiB,cAAc,GAAG,KAAK,UAAU,OAAO,SAAS,oBAAoB,cAAc,CAAC;AAAA,IACzI,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,qBAAqB;AACnB,iBAAa,KAAK,cAAc;AAChC,UAAM,cAAc,KAAK,cAAc;AACvC,SAAK,iBAAiB,KAAK,WAAW;AACtC,QAAI,aAAa;AACf,WAAK,aAAa,KAAK,KAAK,YAAY,cAAc,YAAY;AAAA,IACpE;AAAA,EACF;AAAA;AAAA,EAEA,kBAAkB;AAChB,QAAI,KAAK,cAAc,UAAU;AAC/B,WAAK,YAAY,KAAK;AAAA,IACxB,WAAW,KAAK,sBAAsB,UAAU;AAC9C,WAAK,oBAAoB,KAAK;AAAA,IAChC;AAAA,EACF;AAAA;AAAA,EAEA,gBAAgB,UAAU;AACxB,SAAK,YAAY,cAAc,UAAU,OAAO,2BAA2B,QAAQ;AAAA,EACrF;AAAA;AAAA,EAEA,sBAAsB;AACpB,WAAO,KAAK,QAAQ,KAAK,KAAK,UAAU,QAAQ,QAAQ;AAAA,EAC1D;AAAA;AAAA,EAEA,oBAAoB;AAClB,WAAO,KAAK,mBAAmB;AAAA,EACjC;AAAA;AAAA,EAEA,+BAA+B,MAAM,KAAK,oBAAoB,GAAG;AAC/D,SAAK,oBAAoB,KAAK;AAC9B,QAAI,KAAK,iBAAiB,GAAG;AAC3B,WAAK,YAAY,OAAO,QAAQ,SAAS;AAAA,IAC3C,WAAW,KAAK,iBAAiB,GAAG;AAClC,WAAK,YAAY,OAAO,QAAQ,UAAU;AAAA,IAC5C,OAAO;AACL,WAAK,YAAY;AAAA,IACnB;AACA,QAAI,KAAK,oBAAoB,GAAG;AAC9B,WAAK,0BAA0B;AAAA,IACjC,WAAW,KAAK,iBAAiB,KAAK,cAAc,YAAY,KAAK,sBAAsB,WAAW;AAIpG,mBAAa,KAAK,cAAc;AAChC,WAAK,iBAAiB,KAAK,QAAQ,kBAAkB,MAAM,WAAW,MAAM,KAAK,0BAA0B,GAAG,GAAG,CAAC;AAAA,IACpH;AAAA,EACF;AAAA;AAAA,EAEA,4BAA4B;AAC1B,SAAK,mBAAmB;AACxB,oBAAgB,MAAM,KAAK,gBAAgB,GAAG;AAAA,MAC5C,UAAU,KAAK;AAAA,IACjB,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,sBAAsB;AACpB,WAAO,KAAK,yBAAyB,KAAK,sBAAsB,SAAS,KAAK,sBAAsB;AAAA,EACtG;AAAA,EACA,OAAO,OAAO,SAAS,mBAAmB,mBAAmB;AAC3D,WAAO,KAAK,qBAAqB,aAAY;AAAA,EAC/C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,IAC5B,WAAW,SAAS,iBAAiB,IAAI,KAAK;AAC5C,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,kBAAkB,CAAC;AAClC,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,cAAc,GAAG;AAClE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,kBAAkB,GAAG;AAAA,MACxE;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,kBAAkB;AAAA,IACjC,UAAU;AAAA,IACV,cAAc,SAAS,wBAAwB,IAAI,KAAK;AACtD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,SAAS,IAAI,cAAc,WAAW,OAAO,EAAE;AAAA,MAChE;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,UAAU,CAAC,GAAG,WAAW,UAAU;AAAA,MACnC,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,UAAU;AAAA,IACZ;AAAA,IACA,SAAS;AAAA,MACP,cAAc;AAAA,MACd,kBAAkB;AAAA,MAClB,aAAa;AAAA,IACf;AAAA,IACA,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,WAAW,EAAE,GAAG,CAAC,iBAAiB,IAAI,GAAG,0BAA0B,GAAG,CAAC,kBAAkB,EAAE,CAAC;AAAA,IACtG,UAAU,SAAS,oBAAoB,IAAI,KAAK;AAC9C,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,QAAG,WAAW,GAAG,mCAAmC,GAAG,GAAG,eAAe,CAAC;AAC1E,QAAG,aAAa;AAAA,MAClB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,6BAA6B,IAAI,cAAc,MAAM,EAAE,8BAA8B,IAAI,cAAc,OAAO,EAAE,oCAAoC,IAAI,cAAc,YAAY,IAAI,sBAAsB,QAAQ;AAAA,MACrO;AAAA,IACF;AAAA,IACA,cAAc,CAAC,kBAAkB,aAAa;AAAA,IAC9C,QAAQ,CAAC,u9BAAu9B;AAAA,IACh+B,eAAe;AAAA,EACjB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,MAAM;AAAA,QACJ,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,QAKT,gBAAgB;AAAA,MAClB;AAAA,MACA,SAAS,CAAC,kBAAkB,aAAa;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,u9BAAu9B;AAAA,IACl+B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,cAAc,OAAO,UAAU;AAAA,EAC/B,qBAAqB,OAAO,iBAAiB;AAAA,EAC7C,UAAU,OAAO,MAAM;AAAA,EACvB,oBAAoB,aAAa;AAAA,EACjC,wBAAwB,aAAa;AAAA,EACrC,uBAAuB,aAAa;AAAA,EACpC,wBAAwB,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA,EAK5C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA,QAAQ,IAAI,UAAU;AAAA;AAAA,EAEtB,iBAAiB;AAAA;AAAA,EAEjB,uBAAuB;AAAA;AAAA,EAEvB,wBAAwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQxB;AAAA;AAAA,EAEA,IAAI,qBAAqB;AACvB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,mBAAmB,OAAO;AAC5B,SAAK,sBAAsB;AAC3B,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA,EACA,sBAAsB;AAAA;AAAA,EAEtB,cAAc;AAAA;AAAA,EAEd,YAAY;AAAA;AAAA,EAEZ,gBAAgB;AAAA;AAAA,EAEhB,IAAI,gBAAgB;AAClB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,cAAc,OAAO;AACvB,SAAK,iBAAiB,MAAM,KAAK,IAAI,OAAO;AAAA,EAC9C;AAAA,EACA,iBAAiB;AAAA;AAAA,EAEjB,iBAAiB;AAAA;AAAA,EAEjB,IAAI,oBAAoB;AACtB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,kBAAkB,OAAO;AAC3B,UAAM,cAAc,QAAQ;AAC5B,SAAK,qBAAqB,QAAQ,KAAK,WAAW,IAAI,QAAQ,OAAO;AAAA,EACvE;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,kBAAkB;AACpB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,gBAAgB,OAAO;AACzB,SAAK,mBAAmB,MAAM,KAAK,IAAI,OAAO;AAAA,EAChD;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB;AAAA;AAAA,EAEpB,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMhB,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWlB,IAAI,kBAAkB;AACpB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,gBAAgB,OAAO;AACzB,UAAM,YAAY,KAAK,YAAY,cAAc;AACjD,cAAU,OAAO,4BAA4B,kBAAkB,KAAK,eAAe,EAAE;AACrF,QAAI,OAAO;AACT,gBAAU,IAAI,4BAA4B,kBAAkB,KAAK,EAAE;AAAA,IACrE;AACA,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,sBAAsB,IAAI,aAAa;AAAA;AAAA,EAEvC,cAAc,IAAI,aAAa;AAAA;AAAA,EAE/B,gBAAgB,IAAI,aAAa;AAAA;AAAA,EAEjC,oBAAoB,IAAI,aAAa,IAAI;AAAA,EACzC;AAAA;AAAA,EAEA,YAAY,CAAC,OAAO,QAAQ,EAAE;AAAA,EAC9B,cAAc;AACZ,UAAM,gBAAgB,OAAO,iBAAiB;AAAA,MAC5C,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,WAAW,OAAO,YAAY,EAAE,MAAM,gBAAgB;AAC3D,SAAK,oBAAoB,iBAAiB,cAAc,oBAAoB,cAAc,oBAAoB;AAC9G,SAAK,oBAAoB,iBAAiB,cAAc,qBAAqB,OAAO,cAAc,oBAAoB;AACtH,SAAK,gBAAgB,iBAAiB,cAAc,iBAAiB,OAAO,cAAc,gBAAgB;AAC1G,QAAI,eAAe,mBAAmB,MAAM;AAC1C,WAAK,kBAAkB,cAAc;AAAA,IACvC;AACA,SAAK,kBAAkB,CAAC,CAAC,eAAe;AACxC,SAAK,qBAAqB,iBAAiB,cAAc,sBAAsB,OAAO,cAAc,qBAAqB;AACzH,SAAK,cAAc,iBAAiB,cAAc,eAAe,OAAO,cAAc,cAAc;AACpG,SAAK,YAAY,iBAAiB,cAAc,aAAa,OAAO,cAAc,YAAY;AAAA,EAChG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,wBAAwB;AAGtB,UAAM,gBAAgB,KAAK,iBAAiB,KAAK,eAAe,KAAK,cAAc;AAGnF,QAAI,KAAK,kBAAkB,eAAe;AACxC,YAAM,aAAa,KAAK,kBAAkB;AAC1C,UAAI,CAAC,YAAY;AACf,aAAK,kBAAkB,KAAK,KAAK,mBAAmB,aAAa,CAAC;AAGlE,cAAM,UAAU,KAAK,gBAAgB;AACrC,gBAAQ,MAAM,YAAY,QAAQ,eAAe;AAAA,MACnD;AAGA,cAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,aAAK,MAAM,QAAQ,CAAC,KAAK,UAAU,IAAI,WAAW,UAAU,aAAa;AACzE,YAAI,CAAC,YAAY;AACf,eAAK,oBAAoB,KAAK,aAAa;AAG3C,eAAK,gBAAgB,cAAc,MAAM,YAAY;AAAA,QACvD;AAAA,MACF,CAAC;AAAA,IACH;AAEA,SAAK,MAAM,QAAQ,CAAC,KAAK,UAAU;AACjC,UAAI,WAAW,QAAQ;AAGvB,UAAI,KAAK,kBAAkB,QAAQ,IAAI,YAAY,KAAK,CAAC,IAAI,QAAQ;AACnE,YAAI,SAAS,gBAAgB,KAAK;AAAA,MACpC;AAAA,IACF,CAAC;AACD,QAAI,KAAK,mBAAmB,eAAe;AACzC,WAAK,iBAAiB;AACtB,WAAK,uBAAuB;AAC5B,WAAK,mBAAmB,aAAa;AAAA,IACvC;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,SAAK,0BAA0B;AAC/B,SAAK,sBAAsB;AAG3B,SAAK,oBAAoB,KAAK,MAAM,QAAQ,UAAU,MAAM;AAC1D,YAAM,gBAAgB,KAAK,eAAe,KAAK,cAAc;AAG7D,UAAI,kBAAkB,KAAK,gBAAgB;AACzC,cAAM,OAAO,KAAK,MAAM,QAAQ;AAChC,YAAI;AACJ,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,cAAI,KAAK,CAAC,EAAE,UAAU;AAIpB,iBAAK,iBAAiB,KAAK,iBAAiB;AAC5C,iBAAK,uBAAuB;AAC5B,0BAAc,KAAK,CAAC;AACpB;AAAA,UACF;AAAA,QACF;AAIA,YAAI,CAAC,eAAe,KAAK,aAAa,GAAG;AACvC,kBAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,iBAAK,aAAa,EAAE,WAAW;AAC/B,iBAAK,kBAAkB,KAAK,KAAK,mBAAmB,aAAa,CAAC;AAAA,UACpE,CAAC;AAAA,QACH;AAAA,MACF;AACA,WAAK,mBAAmB,aAAa;AAAA,IACvC,CAAC;AAAA,EACH;AAAA,EACA,kBAAkB;AAChB,SAAK,uBAAuB,KAAK,WAAW,QAAQ,UAAU,MAAM,KAAK,cAAc,IAAI,CAAC;AAAA,EAC9F;AAAA;AAAA,EAEA,4BAA4B;AAI1B,SAAK,SAAS,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,UAAQ;AACrE,WAAK,MAAM,MAAM,KAAK,OAAO,SAAO;AAClC,eAAO,IAAI,qBAAqB,QAAQ,CAAC,IAAI;AAAA,MAC/C,CAAC,CAAC;AACF,WAAK,MAAM,gBAAgB;AAAA,IAC7B,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,MAAM,QAAQ;AACnB,SAAK,kBAAkB,YAAY;AACnC,SAAK,sBAAsB,YAAY;AACvC,SAAK,qBAAqB,YAAY;AAAA,EACxC;AAAA;AAAA,EAEA,gBAAgB;AACd,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,0BAA0B;AAAA,IAC5C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,mBAAmB;AACjB,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,iBAAiB;AAAA,IACnC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,OAAO;AACd,UAAM,SAAS,KAAK;AACpB,QAAI,QAAQ;AACV,aAAO,aAAa;AAAA,IACtB;AAAA,EACF;AAAA,EACA,cAAc,OAAO;AACnB,SAAK,uBAAuB;AAC5B,SAAK,YAAY,KAAK,KAAK,mBAAmB,KAAK,CAAC;AAAA,EACtD;AAAA,EACA,mBAAmB,OAAO;AACxB,UAAM,QAAQ,IAAI,kBAAkB;AACpC,UAAM,QAAQ;AACd,QAAI,KAAK,SAAS,KAAK,MAAM,QAAQ;AACnC,YAAM,MAAM,KAAK,MAAM,QAAQ,EAAE,KAAK;AAAA,IACxC;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,wBAAwB;AACtB,QAAI,KAAK,uBAAuB;AAC9B,WAAK,sBAAsB,YAAY;AAAA,IACzC;AACA,SAAK,wBAAwB,MAAM,GAAG,KAAK,MAAM,IAAI,SAAO,IAAI,aAAa,CAAC,EAAE,UAAU,MAAM,KAAK,mBAAmB,aAAa,CAAC;AAAA,EACxI;AAAA;AAAA,EAEA,eAAe,OAAO;AAIpB,WAAO,KAAK,IAAI,KAAK,MAAM,SAAS,GAAG,KAAK,IAAI,SAAS,GAAG,CAAC,CAAC;AAAA,EAChE;AAAA;AAAA,EAEA,eAAe,KAAK,OAAO;AACzB,WAAO,IAAI,MAAM,GAAG,KAAK,QAAQ,UAAU,KAAK;AAAA,EAClD;AAAA;AAAA,EAEA,iBAAiB,OAAO;AACtB,WAAO,GAAG,KAAK,QAAQ,YAAY,KAAK;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,yBAAyB,WAAW;AAClC,QAAI,CAAC,KAAK,iBAAiB,CAAC,KAAK,uBAAuB;AACtD,WAAK,wBAAwB;AAC7B;AAAA,IACF;AACA,UAAM,UAAU,KAAK,gBAAgB;AACrC,YAAQ,MAAM,SAAS,KAAK,wBAAwB;AAGpD,QAAI,KAAK,gBAAgB,cAAc,cAAc;AACnD,cAAQ,MAAM,SAAS,YAAY;AAAA,IACrC;AAAA,EACF;AAAA;AAAA,EAEA,8BAA8B;AAC5B,UAAM,UAAU,KAAK,gBAAgB;AACrC,SAAK,wBAAwB,QAAQ;AACrC,YAAQ,MAAM,SAAS;AACvB,SAAK,QAAQ,IAAI,MAAM,KAAK,cAAc,KAAK,CAAC;AAAA,EAClD;AAAA;AAAA,EAEA,aAAa,KAAK,WAAW,OAAO;AAClC,cAAU,aAAa;AACvB,QAAI,CAAC,IAAI,UAAU;AACjB,WAAK,gBAAgB;AAAA,IACvB;AAAA,EACF;AAAA;AAAA,EAEA,aAAa,OAAO;AAClB,UAAM,cAAc,KAAK,wBAAwB,KAAK;AACtD,WAAO,UAAU,cAAc,IAAI;AAAA,EACrC;AAAA;AAAA,EAEA,iBAAiB,aAAa,OAAO;AAKnC,QAAI,eAAe,gBAAgB,WAAW,gBAAgB,SAAS;AACrE,WAAK,WAAW,aAAa;AAAA,IAC/B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,UAAU;AAQtB,QAAI,UAAU;AACZ,WAAK,YAAY,QAAQ,CAAC,MAAM,MAAM,KAAK,gBAAgB,MAAM,KAAK,cAAc,CAAC;AAAA,IACvF;AAAA,EACF;AAAA,EACA,sBAAsB;AACpB,WAAO,KAAK,yBAAyB,KAAK,sBAAsB,OAAO,KAAK,sBAAsB;AAAA,EACpG;AAAA,EACA,OAAO,OAAO,SAAS,oBAAoB,mBAAmB;AAC5D,WAAO,KAAK,qBAAqB,cAAa;AAAA,EAChD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,eAAe,CAAC;AAAA,IAC7B,gBAAgB,SAAS,2BAA2B,IAAI,KAAK,UAAU;AACrE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,QAAQ,CAAC;AAAA,MACvC;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,WAAW;AAAA,MAC9D;AAAA,IACF;AAAA,IACA,WAAW,SAAS,kBAAkB,IAAI,KAAK;AAC7C,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AACrB,QAAG,YAAY,KAAK,CAAC;AACrB,QAAG,YAAY,YAAY,CAAC;AAAA,MAC9B;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,kBAAkB,GAAG;AACtE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,aAAa,GAAG;AACjE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,aAAa;AAAA,MAChE;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,mBAAmB;AAAA,IAClC,UAAU;AAAA,IACV,cAAc,SAAS,yBAAyB,IAAI,KAAK;AACvD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,kBAAkB,IAAI,SAAS;AAC9C,QAAG,WAAW,UAAU,IAAI,SAAS,UAAU;AAC/C,QAAG,YAAY,gCAAgC,IAAI,iBAAiB;AACpE,QAAG,YAAY,oCAAoC,IAAI,aAAa,EAAE,qCAAqC,IAAI,mBAAmB,OAAO,EAAE,kCAAkC,IAAI,WAAW;AAAA,MAC9L;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,oBAAoB,CAAC,GAAG,sBAAsB,sBAAsB,gBAAgB;AAAA,MACpF,aAAa,CAAC,GAAG,oBAAoB,eAAe,gBAAgB;AAAA,MACpE,WAAW,CAAC,GAAG,kBAAkB,WAAW;AAAA,MAC5C,eAAe,CAAC,GAAG,iBAAiB,iBAAiB,gBAAgB;AAAA,MACrE,eAAe,CAAC,GAAG,iBAAiB,iBAAiB,eAAe;AAAA,MACpE,gBAAgB;AAAA,MAChB,mBAAmB;AAAA,MACnB,iBAAiB,CAAC,GAAG,mBAAmB,mBAAmB,eAAe;AAAA,MAC1E,mBAAmB,CAAC,GAAG,qBAAqB,qBAAqB,gBAAgB;AAAA,MACjF,eAAe,CAAC,GAAG,iBAAiB,iBAAiB,gBAAgB;AAAA,MACrE,iBAAiB,CAAC,GAAG,mBAAmB,mBAAmB,gBAAgB;AAAA,MAC3E,iBAAiB;AAAA,MACjB,WAAW,CAAC,GAAG,cAAc,WAAW;AAAA,MACxC,gBAAgB,CAAC,GAAG,mBAAmB,gBAAgB;AAAA,IACzD;AAAA,IACA,SAAS;AAAA,MACP,qBAAqB;AAAA,MACrB,aAAa;AAAA,MACb,eAAe;AAAA,MACf,mBAAmB;AAAA,IACrB;AAAA,IACA,UAAU,CAAC,aAAa;AAAA,IACxB,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,CAAC;AAAA,IACH,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,aAAa,EAAE,GAAG,CAAC,kBAAkB,EAAE,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,GAAG,gBAAgB,sBAAsB,iBAAiB,iBAAiB,qBAAqB,cAAc,iBAAiB,GAAG,CAAC,QAAQ,OAAO,sBAAsB,IAAI,0BAA0B,IAAI,GAAG,WAAW,eAAe,uBAAuB,GAAG,MAAM,mBAAmB,SAAS,YAAY,oBAAoB,GAAG,CAAC,GAAG,0BAA0B,GAAG,CAAC,QAAQ,YAAY,GAAG,MAAM,SAAS,WAAW,YAAY,qBAAqB,iBAAiB,GAAG,CAAC,QAAQ,OAAO,sBAAsB,IAAI,0BAA0B,IAAI,GAAG,WAAW,eAAe,uBAAuB,GAAG,SAAS,kBAAkB,MAAM,YAAY,oBAAoB,GAAG,CAAC,GAAG,iBAAiB,GAAG,CAAC,cAAc,IAAI,GAAG,sBAAsB,GAAG,oBAAoB,mBAAmB,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,GAAG,iBAAiB,GAAG,CAAC,QAAQ,YAAY,GAAG,eAAe,gBAAgB,oBAAoB,MAAM,WAAW,YAAY,qBAAqB,iBAAiB,CAAC;AAAA,IACpiC,UAAU,SAAS,qBAAqB,IAAI,KAAK;AAC/C,UAAI,KAAK,GAAG;AACV,cAAM,MAAS,iBAAiB;AAChC,QAAG,gBAAgB;AACnB,QAAG,eAAe,GAAG,kBAAkB,GAAG,CAAC;AAC3C,QAAG,WAAW,gBAAgB,SAAS,4DAA4D,QAAQ;AACzG,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,cAAc,MAAM,CAAC;AAAA,QACjD,CAAC,EAAE,sBAAsB,SAAS,kEAAkE,QAAQ;AAC1G,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,gBAAgB,MAAM;AAAA,QAClD,CAAC;AACD,QAAG,iBAAiB,GAAG,4BAA4B,GAAG,IAAI,OAAO,GAAM,yBAAyB;AAChG,QAAG,aAAa;AAChB,QAAG,oBAAoB,GAAG,oCAAoC,GAAG,CAAC;AAClE,QAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,QAAG,iBAAiB,GAAG,4BAA4B,GAAG,IAAI,gBAAgB,GAAM,yBAAyB;AACzG,QAAG,aAAa;AAAA,MAClB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,iBAAiB,IAAI,iBAAiB,CAAC,EAAE,iBAAiB,IAAI,aAAa,EAAE,qBAAqB,IAAI,iBAAiB,EAAE,cAAc,IAAI,SAAS,EAAE,mBAAmB,IAAI,cAAc;AACzM,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,IAAI,KAAK;AACvB,QAAG,UAAU,CAAC;AACd,QAAG,cAAc,IAAI,YAAY,IAAI,EAAE;AACvC,QAAG,UAAU;AACb,QAAG,YAAY,2BAA2B,IAAI,oBAAoB,CAAC;AACnE,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,IAAI,KAAK;AAAA,MACzB;AAAA,IACF;AAAA,IACA,cAAc,CAAC,cAAc,oBAAoB,iBAAiB,WAAW,iBAAiB,UAAU;AAAA,IACxG,QAAQ,CAAC,oxNAAsxN;AAAA,IAC/xN,eAAe;AAAA,EACjB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,WAAW;AAAA,QACX,4CAA4C;AAAA,QAC5C,6CAA6C;AAAA,QAC7C,0CAA0C;AAAA,QAC1C,yBAAyB;AAAA,QACzB,wCAAwC;AAAA,MAC1C;AAAA,MACA,SAAS,CAAC,cAAc,oBAAoB,iBAAiB,WAAW,iBAAiB,UAAU;AAAA,MACnG,UAAU;AAAA,MACV,QAAQ,CAAC,oxNAAsxN;AAAA,IACjyN,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,QACb,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,IACnB,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,MACT,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAEH,IAAM,oBAAN,MAAwB;AAAA;AAAA,EAEtB;AAAA;AAAA,EAEA;AACF;AAMA,IAAM,YAAN,MAAM,mBAAkB,sBAAsB;AAAA,EAC5C,eAAe,OAAO,IAAI;AAAA;AAAA,EAE1B,IAAI,qBAAqB;AACvB,WAAO,KAAK,oBAAoB;AAAA,EAClC;AAAA,EACA,IAAI,mBAAmB,OAAO;AAC5B,SAAK,oBAAoB,KAAK,KAAK;AACnC,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA,EACA,sBAAsB,IAAI,gBAAgB,KAAK;AAAA;AAAA,EAE/C,cAAc;AAAA,EACd,IAAI,oBAAoB;AACtB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,kBAAkB,OAAO;AAC3B,UAAM,cAAc,QAAQ;AAC5B,SAAK,qBAAqB,QAAQ,KAAK,WAAW,IAAI,QAAQ,OAAO;AAAA,EACvE;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,kBAAkB;AACpB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,gBAAgB,OAAO;AACzB,UAAM,YAAY,KAAK,YAAY,cAAc;AACjD,cAAU,OAAO,4BAA4B,kBAAkB,KAAK,eAAe,EAAE;AACrF,QAAI,OAAO;AACT,gBAAU,IAAI,4BAA4B,kBAAkB,KAAK,EAAE;AAAA,IACrE;AACA,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,gBAAgB;AAClB,WAAO,KAAK,eAAe;AAAA,EAC7B;AAAA,EACA,IAAI,cAAc,OAAO;AACvB,SAAK,eAAe,IAAI,KAAK;AAAA,EAC/B;AAAA,EACA,iBAAiB,OAAO,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ7B,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMR;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,cAAc;AACZ,UAAM,gBAAgB,OAAO,iBAAiB;AAAA,MAC5C,UAAU;AAAA,IACZ,CAAC;AACD,UAAM;AACN,SAAK,oBAAoB,iBAAiB,cAAc,qBAAqB,OAAO,cAAc,oBAAoB;AACtH,SAAK,qBAAqB,iBAAiB,cAAc,sBAAsB,OAAO,cAAc,qBAAqB;AACzH,SAAK,cAAc,iBAAiB,cAAc,eAAe,OAAO,cAAc,cAAc;AAAA,EACtG;AAAA,EACA,gBAAgB;AAAA,EAEhB;AAAA,EACA,qBAAqB;AACnB,SAAK,UAAU,IAAI,UAAU,KAAK,MAAM;AAGxC,SAAK,OAAO,QAAQ,KAAK,UAAU,IAAI,GAAG,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM,KAAK,iBAAiB,CAAC;AAC7G,UAAM,mBAAmB;AAEzB,SAAK,YAAY,OAAO,KAAK,UAAU,IAAI,GAAG,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM,KAAK,aAAa,IAAI,KAAK,aAAa,cAAc,IAAI,CAAC;AAAA,EACvJ;AAAA,EACA,kBAAkB;AAChB,QAAI,CAAC,KAAK,aAAa,OAAO,cAAc,eAAe,YAAY;AACrE,YAAM,IAAI,MAAM,uDAAuD;AAAA,IACzE;AACA,UAAM,gBAAgB;AAAA,EACxB;AAAA;AAAA,EAEA,mBAAmB;AACjB,QAAI,CAAC,KAAK,QAAQ;AAChB;AAAA,IACF;AACA,UAAM,QAAQ,KAAK,OAAO,QAAQ;AAClC,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,UAAI,MAAM,CAAC,EAAE,QAAQ;AACnB,aAAK,gBAAgB;AACrB,YAAI,KAAK,UAAU;AACjB,eAAK,SAAS,eAAe,MAAM,CAAC,EAAE;AAAA,QACxC;AAGA,aAAK,aAAa,IAAI,MAAM,CAAC,CAAC;AAC9B,aAAK,mBAAmB,aAAa;AACrC;AAAA,MACF;AAAA,IACF;AACA,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,WAAW;AACT,WAAO,KAAK,WAAW,YAAY,KAAK,YAAY,cAAc,aAAa,MAAM;AAAA,EACvF;AAAA,EACA,UAAU,MAAM;AACd,WAAO,KAAK,aAAa,eAAe;AAAA,EAC1C;AAAA,EACA,OAAO,OAAO,SAAS,kBAAkB,mBAAmB;AAC1D,WAAO,KAAK,qBAAqB,YAAW;AAAA,EAC9C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,IACvC,gBAAgB,SAAS,yBAAyB,IAAI,KAAK,UAAU;AACnE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,YAAY,CAAC;AAAA,MAC3C;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,SAAS;AAAA,MAC5D;AAAA,IACF;AAAA,IACA,WAAW,SAAS,gBAAgB,IAAI,KAAK;AAC3C,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AACrB,QAAG,YAAY,KAAK,CAAC;AACrB,QAAG,YAAY,KAAK,CAAC;AACrB,QAAG,YAAY,KAAK,CAAC;AACrB,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,oBAAoB,GAAG;AACxE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,WAAW,GAAG;AAC/D,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB,GAAG;AACpE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,iBAAiB,GAAG;AACrE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,qBAAqB,GAAG;AAAA,MAC3E;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,uBAAuB,oBAAoB;AAAA,IAC1D,UAAU;AAAA,IACV,cAAc,SAAS,uBAAuB,IAAI,KAAK;AACrD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,QAAQ,IAAI,SAAS,CAAC;AACrC,QAAG,YAAY,gCAAgC,IAAI,iBAAiB;AACpE,QAAG,YAAY,kDAAkD,IAAI,uBAAuB,EAAE,0BAA0B,IAAI,oBAAoB,KAAK,KAAK,EAAE,oCAAoC,IAAI,WAAW,EAAE,eAAe,IAAI,UAAU,UAAU,IAAI,UAAU,QAAQ,EAAE,cAAc,IAAI,UAAU,QAAQ,EAAE,YAAY,IAAI,UAAU,MAAM,EAAE,2BAA2B,IAAI,mBAAmB;AAAA,MAC5Y;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB,CAAC,GAAG,sBAAsB,sBAAsB,gBAAgB;AAAA,MACpF,aAAa,CAAC,GAAG,oBAAoB,eAAe,gBAAgB;AAAA,MACpE,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,eAAe,CAAC,GAAG,iBAAiB,iBAAiB,gBAAgB;AAAA,MACrE,OAAO;AAAA,MACP,UAAU;AAAA,IACZ;AAAA,IACA,UAAU,CAAC,gBAAgB,WAAW;AAAA,IACtC,UAAU,CAAI,0BAA0B;AAAA,IACxC,OAAO;AAAA,IACP,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,qBAAqB,EAAE,GAAG,CAAC,oBAAoB,EAAE,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,gBAAgB,EAAE,GAAG,CAAC,iBAAiB,EAAE,GAAG,CAAC,cAAc,IAAI,GAAG,iCAAiC,wCAAwC,GAAG,SAAS,aAAa,YAAY,mBAAmB,GAAG,CAAC,GAAG,uCAAuC,GAAG,CAAC,GAAG,8BAA8B,GAAG,SAAS,GAAG,CAAC,GAAG,oBAAoB,GAAG,mBAAmB,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,cAAc,IAAI,GAAG,iCAAiC,uCAAuC,GAAG,aAAa,SAAS,YAAY,mBAAmB,CAAC;AAAA,IACvlB,UAAU,SAAS,mBAAmB,IAAI,KAAK;AAC7C,UAAI,KAAK,GAAG;AACV,cAAM,MAAS,iBAAiB;AAChC,QAAG,gBAAgB;AACnB,QAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,QAAG,WAAW,SAAS,SAAS,0CAA0C;AACxE,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,sBAAsB,QAAQ,CAAC;AAAA,QAC3D,CAAC,EAAE,aAAa,SAAS,4CAA4C,QAAQ;AAC3E,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,sBAAsB,UAAU,MAAM,CAAC;AAAA,QACnE,CAAC,EAAE,YAAY,SAAS,6CAA6C;AACnE,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,cAAc,CAAC;AAAA,QAC3C,CAAC;AACD,QAAG,UAAU,GAAG,OAAO,CAAC;AACxB,QAAG,aAAa;AAChB,QAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,QAAG,WAAW,WAAW,SAAS,0CAA0C,QAAQ;AAClF,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,eAAe,MAAM,CAAC;AAAA,QAClD,CAAC;AACD,QAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,QAAG,WAAW,qBAAqB,SAAS,sDAAsD;AAChG,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,kBAAkB,CAAC;AAAA,QAC/C,CAAC;AACD,QAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,QAAG,aAAa,CAAC;AACjB,QAAG,aAAa,EAAE,EAAE;AACpB,QAAG,eAAe,IAAI,OAAO,IAAI,CAAC;AAClC,QAAG,WAAW,aAAa,SAAS,6CAA6C,QAAQ;AACvF,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,sBAAsB,SAAS,MAAM,CAAC;AAAA,QAClE,CAAC,EAAE,SAAS,SAAS,2CAA2C;AAC9D,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,sBAAsB,OAAO,CAAC;AAAA,QAC1D,CAAC,EAAE,YAAY,SAAS,8CAA8C;AACpE,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,cAAc,CAAC;AAAA,QAC3C,CAAC;AACD,QAAG,UAAU,IAAI,OAAO,CAAC;AACzB,QAAG,aAAa;AAAA,MAClB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,0CAA0C,IAAI,oBAAoB;AACjF,QAAG,WAAW,qBAAqB,IAAI,wBAAwB,IAAI,aAAa;AAChF,QAAG,UAAU,EAAE;AACf,QAAG,YAAY,0CAA0C,IAAI,mBAAmB;AAChF,QAAG,WAAW,qBAAqB,IAAI,uBAAuB,IAAI,aAAa;AAAA,MACjF;AAAA,IACF;AAAA,IACA,cAAc,CAAC,WAAW,iBAAiB;AAAA,IAC3C,QAAQ,CAAC,4vLAA4vL;AAAA,IACrwL,eAAe;AAAA,EACjB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,eAAe;AAAA,QACf,SAAS;AAAA,QACT,0DAA0D;AAAA,QAC1D,kCAAkC;AAAA,QAClC,4CAA4C;AAAA,QAC5C,uBAAuB;AAAA,QACvB,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,mCAAmC;AAAA,QACnC,wCAAwC;AAAA,MAC1C;AAAA,MACA,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,SAAS,CAAC,WAAW,iBAAiB;AAAA,MACtC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,4vLAA4vL;AAAA,IACvwL,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,WAAW,MAAM,UAAU,GAAG;AAAA,QACnC,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,QACzB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,QAChB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,QACrB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAIH,IAAM,aAAN,MAAM,oBAAmB,WAAW;AAAA,EAClC,aAAa,OAAO,SAAS;AAAA,EAC7B,aAAa,OAAO,UAAU;AAAA,EAC9B,gBAAgB,OAAO,YAAY;AAAA,EACnC,aAAa,IAAI,QAAQ;AAAA;AAAA,EAEzB,YAAY;AAAA,EACZ,YAAY,SAAS,MAAM,KAAK,WAAW,aAAa,MAAM,OAAO,KAAK,WAAW,EAAE;AAAA;AAAA,EAEvF,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,OAAO,OAAO;AAChB,QAAI,UAAU,KAAK,WAAW;AAC5B,WAAK,YAAY;AACjB,WAAK,WAAW,iBAAiB;AAAA,IACnC;AAAA,EACF;AAAA;AAAA,EAEA,WAAW;AAAA;AAAA,EAEX,IAAI,gBAAgB;AAClB,WAAO,KAAK,eAAe;AAAA,EAC7B;AAAA,EACA,IAAI,cAAc,OAAO;AACvB,SAAK,eAAe,IAAI,KAAK;AAAA,EAC/B;AAAA,EACA,iBAAiB,OAAO,KAAK;AAAA,EAC7B,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,iBAAiB;AACnB,WAAO,KAAK,YAAY,KAAK,iBAAiB,KAAK,WAAW,iBAAiB,CAAC,CAAC,KAAK,aAAa;AAAA,EACrG;AAAA;AAAA,EAEA,KAAK,OAAO,YAAY,EAAE,MAAM,eAAe;AAAA,EAC/C,cAAc;AACZ,UAAM;AACN,WAAO,sBAAsB,EAAE,KAAK,uBAAuB;AAC3D,UAAM,sBAAsB,OAAO,2BAA2B;AAAA,MAC5D,UAAU;AAAA,IACZ,CAAC;AACD,UAAM,WAAW,OAAO,IAAI,mBAAmB,UAAU,GAAG;AAAA,MAC1D,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,eAAe,uBAAuB,CAAC;AAC5C,SAAK,WAAW,YAAY,OAAO,IAAI,SAAS,QAAQ,KAAK;AAC7D,QAAI,oBAAoB,GAAG;AACzB,WAAK,aAAa,YAAY;AAAA,QAC5B,eAAe;AAAA,QACf,cAAc;AAAA,MAChB;AAAA,IACF;AACA,SAAK,WAAW,oBAAoB,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,wBAAsB;AACnG,WAAK,qBAAqB;AAAA,IAC5B,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,QAAQ;AACN,SAAK,WAAW,cAAc,MAAM;AAAA,EACtC;AAAA,EACA,kBAAkB;AAChB,SAAK,cAAc,QAAQ,KAAK,UAAU;AAAA,EAC5C;AAAA,EACA,cAAc;AACZ,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AACzB,UAAM,YAAY;AAClB,SAAK,cAAc,eAAe,KAAK,UAAU;AAAA,EACnD;AAAA,EACA,eAAe;AAGb,SAAK,WAAW,aAAa,KAAK,WAAW,OAAO,QAAQ,EAAE,QAAQ,IAAI;AAAA,EAC5E;AAAA,EACA,eAAe,OAAO;AACpB,QAAI,MAAM,YAAY,SAAS,MAAM,YAAY,OAAO;AACtD,UAAI,KAAK,UAAU;AACjB,cAAM,eAAe;AAAA,MACvB,WAAW,KAAK,WAAW,UAAU;AAGnC,YAAI,MAAM,YAAY,OAAO;AAC3B,gBAAM,eAAe;AAAA,QACvB;AACA,aAAK,WAAW,cAAc,MAAM;AAAA,MACtC;AAAA,IACF;AAAA,EACF;AAAA,EACA,mBAAmB;AACjB,WAAO,KAAK,WAAW,WAAW,KAAK,WAAW,UAAU,KAAK,KAAK,WAAW,cAAc,aAAa,eAAe;AAAA,EAC7H;AAAA,EACA,mBAAmB;AACjB,QAAI,KAAK,WAAW,UAAU;AAC5B,aAAO,KAAK,SAAS,SAAS;AAAA,IAChC,OAAO;AACL,aAAO,KAAK,WAAW,cAAc,aAAa,eAAe;AAAA,IACnE;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,WAAO,KAAK,UAAU,CAAC,KAAK,WAAW,WAAW,SAAS;AAAA,EAC7D;AAAA,EACA,WAAW;AACT,WAAO,KAAK,WAAW,WAAW,QAAQ,KAAK,WAAW,cAAc,aAAa,MAAM;AAAA,EAC7F;AAAA,EACA,OAAO,OAAO,SAAS,mBAAmB,mBAAmB;AAC3D,WAAO,KAAK,qBAAqB,aAAY;AAAA,EAC/C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,gBAAgB,EAAE,GAAG,CAAC,IAAI,cAAc,EAAE,CAAC;AAAA,IAC5D,WAAW,CAAC,GAAG,WAAW,oBAAoB,qBAAqB;AAAA,IACnE,UAAU;AAAA,IACV,cAAc,SAAS,wBAAwB,IAAI,KAAK;AACtD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,SAAS,SAAS,sCAAsC;AACpE,iBAAO,IAAI,aAAa;AAAA,QAC1B,CAAC,EAAE,WAAW,SAAS,sCAAsC,QAAQ;AACnE,iBAAO,IAAI,eAAe,MAAM;AAAA,QAClC,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,iBAAiB,IAAI,iBAAiB,CAAC,EAAE,gBAAgB,IAAI,gBAAgB,CAAC,EAAE,iBAAiB,IAAI,QAAQ,EAAE,iBAAiB,IAAI,iBAAiB,CAAC,EAAE,MAAM,IAAI,EAAE,EAAE,YAAY,IAAI,UAAU,CAAC,EAAE,QAAQ,IAAI,SAAS,CAAC;AACxO,QAAG,YAAY,wBAAwB,IAAI,QAAQ,EAAE,mBAAmB,IAAI,MAAM;AAAA,MACpF;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,QAAQ,CAAC,GAAG,UAAU,UAAU,gBAAgB;AAAA,MAChD,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,eAAe,CAAC,GAAG,iBAAiB,iBAAiB,gBAAgB;AAAA,MACrE,UAAU,CAAC,GAAG,YAAY,YAAY,WAAS,SAAS,OAAO,IAAI,gBAAgB,KAAK,CAAC;AAAA,MACzF,IAAI;AAAA,IACN;AAAA,IACA,UAAU,CAAC,YAAY;AAAA,IACvB,UAAU,CAAI,0BAA0B;AAAA,IACxC,OAAO;AAAA,IACP,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,iBAAiB,GAAG,CAAC,cAAc,IAAI,GAAG,sBAAsB,GAAG,oBAAoB,mBAAmB,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,qBAAqB,CAAC;AAAA,IAC7K,UAAU,SAAS,oBAAoB,IAAI,KAAK;AAC9C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,UAAU,GAAG,QAAQ,CAAC,EAAE,GAAG,OAAO,CAAC;AACtC,QAAG,eAAe,GAAG,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC;AAC5C,QAAG,aAAa,CAAC;AACjB,QAAG,aAAa,EAAE;AAAA,MACpB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,UAAU;AACb,QAAG,WAAW,oBAAoB,IAAI,WAAW,aAAa,EAAE,qBAAqB,IAAI,cAAc;AAAA,MACzG;AAAA,IACF;AAAA,IACA,cAAc,CAAC,SAAS;AAAA,IACxB,QAAQ,CAAC,q0GAAu0G;AAAA,IACh1G,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,aAAa;AAAA,QACb,mBAAmB;AAAA,QACnB,eAAe;AAAA,QACf,gCAAgC;AAAA,QAChC,2BAA2B;AAAA,QAC3B,WAAW;AAAA,QACX,aAAa;AAAA,MACf;AAAA,MACA,SAAS,CAAC,SAAS;AAAA,MACnB,UAAU;AAAA,MACV,QAAQ,CAAC,q0GAAu0G;AAAA,IACl1G,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW,WAAS,SAAS,OAAO,IAAI,gBAAgB,KAAK;AAAA,MAC/D,CAAC;AAAA,IACH,CAAC;AAAA,IACD,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAIH,IAAM,iBAAN,MAAM,gBAAe;AAAA;AAAA,EAEnB,KAAK,OAAO,YAAY,EAAE,MAAM,oBAAoB;AAAA;AAAA,EAEpD;AAAA,EACA,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,mBAAmB,CAAC;AAAA,IACjC,WAAW,CAAC,QAAQ,YAAY,GAAG,uBAAuB;AAAA,IAC1D,UAAU;AAAA,IACV,cAAc,SAAS,4BAA4B,IAAI,KAAK;AAC1D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,mBAAmB,IAAI,YAAY,EAAE,MAAM,IAAI,EAAE;AAAA,MAClE;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,IAAI;AAAA,IACN;AAAA,IACA,UAAU,CAAC,gBAAgB;AAAA,IAC3B,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,wBAAwB,IAAI,KAAK;AAClD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,aAAa,CAAC;AAAA,MACnB;AAAA,IACF;AAAA,IACA,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,0BAA0B;AAAA,QAC1B,aAAa;AAAA,QACb,SAAS;AAAA,QACT,QAAQ;AAAA,MACV;AAAA,MACA,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,iBAAiB,eAAe,aAAa,QAAQ,aAAa,WAAW,gBAAgB,UAAU;AAAA,IACjH,SAAS,CAAC,iBAAiB,eAAe,aAAa,QAAQ,aAAa,WAAW,gBAAgB,UAAU;AAAA,EACnH,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,iBAAiB,eAAe;AAAA,EAC5C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,eAAe,aAAa,QAAQ,aAAa,WAAW,gBAAgB,UAAU;AAAA,MACjH,SAAS,CAAC,iBAAiB,eAAe,aAAa,QAAQ,aAAa,WAAW,gBAAgB,UAAU;AAAA,IACnH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAQH,IAAM,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA4CxB,cAAc;AAAA,IACZ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,WAAW;AAAA,UACX,YAAY;AAAA,QACd;AAAA,QACA,QAAQ;AAAA,MACV;AAAA,IACF,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,WAAW;AAAA,UACX,WAAW;AAAA,UACX,YAAY;AAAA,QACd;AAAA,QACA,QAAQ;AAAA,MACV;AAAA,IACF,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,WAAW;AAAA,UACX,WAAW;AAAA,UACX,YAAY;AAAA,QACd;AAAA,QACA,QAAQ;AAAA,MACV;AAAA,IACF,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,WAAW;AAAA,QACT,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,SAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,WAAW,CAAC;AAAA,QACV,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,WAAW;AAAA,UACX,YAAY;AAAA,QACd;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,MACX,CAAC;AAAA,MACD,SAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,WAAW,CAAC;AAAA,QACV,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,WAAW;AAAA,UACX,YAAY;AAAA,QACd;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,MACX,CAAC;AAAA,MACD,SAAS;AAAA,IACX,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,EACZ;AACF;", "names": []}