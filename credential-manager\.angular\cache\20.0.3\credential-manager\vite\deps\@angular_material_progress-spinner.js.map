{"version": 3, "sources": ["../../../../../../node_modules/@angular/material/fesm2022/progress-spinner.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, numberAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ViewChild, NgModule } from '@angular/core';\nimport { NgTemplateOutlet } from '@angular/common';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport '@angular/cdk/layout';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\n\n/** Injection token to be used to override the default options for `mat-progress-spinner`. */\nconst _c0 = [\"determinateSpinner\"];\nfunction MatProgressSpinner_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 11);\n    i0.ɵɵelement(1, \"circle\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"viewBox\", ctx_r0._viewBox());\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"stroke-dasharray\", ctx_r0._strokeCircumference(), \"px\")(\"stroke-dashoffset\", ctx_r0._strokeCircumference() / 2, \"px\")(\"stroke-width\", ctx_r0._circleStrokeWidth(), \"%\");\n    i0.ɵɵattribute(\"r\", ctx_r0._circleRadius());\n  }\n}\nconst MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS = new InjectionToken('mat-progress-spinner-default-options', {\n  providedIn: 'root',\n  factory: MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    diameter: BASE_SIZE\n  };\n}\n/**\n * Base reference size of the spinner.\n */\nconst BASE_SIZE = 100;\n/**\n * Base reference stroke width of the spinner.\n */\nconst BASE_STROKE_WIDTH = 10;\nclass MatProgressSpinner {\n  _elementRef = inject(ElementRef);\n  /** Whether the _mat-animation-noopable class should be applied, disabling animations.  */\n  _noopAnimations;\n  // TODO: should be typed as `ThemePalette` but internal apps pass in arbitrary strings.\n  /**\n   * Theme color of the progress spinner. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/progress-spinner/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  get color() {\n    return this._color || this._defaultColor;\n  }\n  set color(value) {\n    this._color = value;\n  }\n  _color;\n  _defaultColor = 'primary';\n  /** The element of the determinate spinner. */\n  _determinateCircle;\n  constructor() {\n    const defaults = inject(MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS);\n    this._noopAnimations = _animationsDisabled() && !!defaults && !defaults._forceAnimations;\n    this.mode = this._elementRef.nativeElement.nodeName.toLowerCase() === 'mat-spinner' ? 'indeterminate' : 'determinate';\n    if (defaults) {\n      if (defaults.color) {\n        this.color = this._defaultColor = defaults.color;\n      }\n      if (defaults.diameter) {\n        this.diameter = defaults.diameter;\n      }\n      if (defaults.strokeWidth) {\n        this.strokeWidth = defaults.strokeWidth;\n      }\n    }\n  }\n  /**\n   * Mode of the progress bar.\n   *\n   * Input must be one of these values: determinate, indeterminate, buffer, query, defaults to\n   * 'determinate'.\n   * Mirrored to mode attribute.\n   */\n  mode;\n  /** Value of the progress bar. Defaults to zero. Mirrored to aria-valuenow. */\n  get value() {\n    return this.mode === 'determinate' ? this._value : 0;\n  }\n  set value(v) {\n    this._value = Math.max(0, Math.min(100, v || 0));\n  }\n  _value = 0;\n  /** The diameter of the progress spinner (will set width and height of svg). */\n  get diameter() {\n    return this._diameter;\n  }\n  set diameter(size) {\n    this._diameter = size || 0;\n  }\n  _diameter = BASE_SIZE;\n  /** Stroke width of the progress spinner. */\n  get strokeWidth() {\n    return this._strokeWidth ?? this.diameter / 10;\n  }\n  set strokeWidth(value) {\n    this._strokeWidth = value || 0;\n  }\n  _strokeWidth;\n  /** The radius of the spinner, adjusted for stroke width. */\n  _circleRadius() {\n    return (this.diameter - BASE_STROKE_WIDTH) / 2;\n  }\n  /** The view box of the spinner's svg element. */\n  _viewBox() {\n    const viewBox = this._circleRadius() * 2 + this.strokeWidth;\n    return `0 0 ${viewBox} ${viewBox}`;\n  }\n  /** The stroke circumference of the svg circle. */\n  _strokeCircumference() {\n    return 2 * Math.PI * this._circleRadius();\n  }\n  /** The dash offset of the svg circle. */\n  _strokeDashOffset() {\n    if (this.mode === 'determinate') {\n      return this._strokeCircumference() * (100 - this._value) / 100;\n    }\n    return null;\n  }\n  /** Stroke width of the circle in percent. */\n  _circleStrokeWidth() {\n    return this.strokeWidth / this.diameter * 100;\n  }\n  static ɵfac = function MatProgressSpinner_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatProgressSpinner)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatProgressSpinner,\n    selectors: [[\"mat-progress-spinner\"], [\"mat-spinner\"]],\n    viewQuery: function MatProgressSpinner_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._determinateCircle = _t.first);\n      }\n    },\n    hostAttrs: [\"role\", \"progressbar\", \"tabindex\", \"-1\", 1, \"mat-mdc-progress-spinner\", \"mdc-circular-progress\"],\n    hostVars: 18,\n    hostBindings: function MatProgressSpinner_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-valuemin\", 0)(\"aria-valuemax\", 100)(\"aria-valuenow\", ctx.mode === \"determinate\" ? ctx.value : null)(\"mode\", ctx.mode);\n        i0.ɵɵclassMap(\"mat-\" + ctx.color);\n        i0.ɵɵstyleProp(\"width\", ctx.diameter, \"px\")(\"height\", ctx.diameter, \"px\")(\"--mat-progress-spinner-size\", ctx.diameter + \"px\")(\"--mat-progress-spinner-active-indicator-width\", ctx.diameter + \"px\");\n        i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._noopAnimations)(\"mdc-circular-progress--indeterminate\", ctx.mode === \"indeterminate\");\n      }\n    },\n    inputs: {\n      color: \"color\",\n      mode: \"mode\",\n      value: [2, \"value\", \"value\", numberAttribute],\n      diameter: [2, \"diameter\", \"diameter\", numberAttribute],\n      strokeWidth: [2, \"strokeWidth\", \"strokeWidth\", numberAttribute]\n    },\n    exportAs: [\"matProgressSpinner\"],\n    decls: 14,\n    vars: 11,\n    consts: [[\"circle\", \"\"], [\"determinateSpinner\", \"\"], [\"aria-hidden\", \"true\", 1, \"mdc-circular-progress__determinate-container\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"focusable\", \"false\", 1, \"mdc-circular-progress__determinate-circle-graphic\"], [\"cx\", \"50%\", \"cy\", \"50%\", 1, \"mdc-circular-progress__determinate-circle\"], [\"aria-hidden\", \"true\", 1, \"mdc-circular-progress__indeterminate-container\"], [1, \"mdc-circular-progress__spinner-layer\"], [1, \"mdc-circular-progress__circle-clipper\", \"mdc-circular-progress__circle-left\"], [3, \"ngTemplateOutlet\"], [1, \"mdc-circular-progress__gap-patch\"], [1, \"mdc-circular-progress__circle-clipper\", \"mdc-circular-progress__circle-right\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"focusable\", \"false\", 1, \"mdc-circular-progress__indeterminate-circle-graphic\"], [\"cx\", \"50%\", \"cy\", \"50%\"]],\n    template: function MatProgressSpinner_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, MatProgressSpinner_ng_template_0_Template, 2, 8, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵelementStart(2, \"div\", 2, 1);\n        i0.ɵɵnamespaceSVG();\n        i0.ɵɵelementStart(4, \"svg\", 3);\n        i0.ɵɵelement(5, \"circle\", 4);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵnamespaceHTML();\n        i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6)(8, \"div\", 7);\n        i0.ɵɵelementContainer(9, 8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(10, \"div\", 9);\n        i0.ɵɵelementContainer(11, 8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"div\", 10);\n        i0.ɵɵelementContainer(13, 8);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        const circle_r2 = i0.ɵɵreference(1);\n        i0.ɵɵadvance(4);\n        i0.ɵɵattribute(\"viewBox\", ctx._viewBox());\n        i0.ɵɵadvance();\n        i0.ɵɵstyleProp(\"stroke-dasharray\", ctx._strokeCircumference(), \"px\")(\"stroke-dashoffset\", ctx._strokeDashOffset(), \"px\")(\"stroke-width\", ctx._circleStrokeWidth(), \"%\");\n        i0.ɵɵattribute(\"r\", ctx._circleRadius());\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngTemplateOutlet\", circle_r2);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngTemplateOutlet\", circle_r2);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngTemplateOutlet\", circle_r2);\n      }\n    },\n    dependencies: [NgTemplateOutlet],\n    styles: [\".mat-mdc-progress-spinner{display:block;overflow:hidden;line-height:0;position:relative;direction:ltr;transition:opacity 250ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-progress-spinner circle{stroke-width:var(--mat-progress-spinner-active-indicator-width, 4px)}.mat-mdc-progress-spinner._mat-animation-noopable,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__determinate-circle{transition:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__spinner-layer,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container{animation:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container circle{stroke-dasharray:0 !important}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle{stroke:currentColor;stroke:CanvasText}}.mdc-circular-progress__determinate-container,.mdc-circular-progress__indeterminate-circle-graphic,.mdc-circular-progress__indeterminate-container,.mdc-circular-progress__spinner-layer{position:absolute;width:100%;height:100%}.mdc-circular-progress__determinate-container{transform:rotate(-90deg)}.mdc-circular-progress--indeterminate .mdc-circular-progress__determinate-container{opacity:0}.mdc-circular-progress__indeterminate-container{font-size:0;letter-spacing:0;white-space:nowrap;opacity:0}.mdc-circular-progress--indeterminate .mdc-circular-progress__indeterminate-container{opacity:1;animation:mdc-circular-progress-container-rotate 1568.2352941176ms linear infinite}.mdc-circular-progress__determinate-circle-graphic,.mdc-circular-progress__indeterminate-circle-graphic{fill:rgba(0,0,0,0)}.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:var(--mat-progress-spinner-active-indicator-color, var(--mat-sys-primary))}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:CanvasText}}.mdc-circular-progress__determinate-circle{transition:stroke-dashoffset 500ms cubic-bezier(0, 0, 0.2, 1)}.mdc-circular-progress__gap-patch{position:absolute;top:0;left:47.5%;box-sizing:border-box;width:5%;height:100%;overflow:hidden}.mdc-circular-progress__gap-patch .mdc-circular-progress__indeterminate-circle-graphic{left:-900%;width:2000%;transform:rotate(180deg)}.mdc-circular-progress__circle-clipper .mdc-circular-progress__indeterminate-circle-graphic{width:200%}.mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{left:-100%}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-left .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-left-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-right-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress__circle-clipper{display:inline-flex;position:relative;width:50%;height:100%;overflow:hidden}.mdc-circular-progress--indeterminate .mdc-circular-progress__spinner-layer{animation:mdc-circular-progress-spinner-layer-rotate 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}@keyframes mdc-circular-progress-container-rotate{to{transform:rotate(360deg)}}@keyframes mdc-circular-progress-spinner-layer-rotate{12.5%{transform:rotate(135deg)}25%{transform:rotate(270deg)}37.5%{transform:rotate(405deg)}50%{transform:rotate(540deg)}62.5%{transform:rotate(675deg)}75%{transform:rotate(810deg)}87.5%{transform:rotate(945deg)}100%{transform:rotate(1080deg)}}@keyframes mdc-circular-progress-left-spin{from{transform:rotate(265deg)}50%{transform:rotate(130deg)}to{transform:rotate(265deg)}}@keyframes mdc-circular-progress-right-spin{from{transform:rotate(-265deg)}50%{transform:rotate(-130deg)}to{transform:rotate(-265deg)}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatProgressSpinner, [{\n    type: Component,\n    args: [{\n      selector: 'mat-progress-spinner, mat-spinner',\n      exportAs: 'matProgressSpinner',\n      host: {\n        'role': 'progressbar',\n        'class': 'mat-mdc-progress-spinner mdc-circular-progress',\n        // set tab index to -1 so screen readers will read the aria-label\n        // Note: there is a known issue with JAWS that does not read progressbar aria labels on FireFox\n        'tabindex': '-1',\n        '[class]': '\"mat-\" + color',\n        '[class._mat-animation-noopable]': `_noopAnimations`,\n        '[class.mdc-circular-progress--indeterminate]': 'mode === \"indeterminate\"',\n        '[style.width.px]': 'diameter',\n        '[style.height.px]': 'diameter',\n        '[style.--mat-progress-spinner-size]': 'diameter + \"px\"',\n        '[style.--mat-progress-spinner-active-indicator-width]': 'diameter + \"px\"',\n        '[attr.aria-valuemin]': '0',\n        '[attr.aria-valuemax]': '100',\n        '[attr.aria-valuenow]': 'mode === \"determinate\" ? value : null',\n        '[attr.mode]': 'mode'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      imports: [NgTemplateOutlet],\n      template: \"<ng-template #circle>\\n  <svg [attr.viewBox]=\\\"_viewBox()\\\" class=\\\"mdc-circular-progress__indeterminate-circle-graphic\\\"\\n       xmlns=\\\"http://www.w3.org/2000/svg\\\" focusable=\\\"false\\\">\\n    <circle [attr.r]=\\\"_circleRadius()\\\"\\n            [style.stroke-dasharray.px]=\\\"_strokeCircumference()\\\"\\n            [style.stroke-dashoffset.px]=\\\"_strokeCircumference() / 2\\\"\\n            [style.stroke-width.%]=\\\"_circleStrokeWidth()\\\"\\n            cx=\\\"50%\\\" cy=\\\"50%\\\"/>\\n  </svg>\\n</ng-template>\\n\\n<!--\\n  All children need to be hidden for screen readers in order to support ChromeVox.\\n  More context in the issue: https://github.com/angular/components/issues/22165.\\n-->\\n<div class=\\\"mdc-circular-progress__determinate-container\\\" aria-hidden=\\\"true\\\" #determinateSpinner>\\n  <svg [attr.viewBox]=\\\"_viewBox()\\\" class=\\\"mdc-circular-progress__determinate-circle-graphic\\\"\\n       xmlns=\\\"http://www.w3.org/2000/svg\\\" focusable=\\\"false\\\">\\n    <circle [attr.r]=\\\"_circleRadius()\\\"\\n            [style.stroke-dasharray.px]=\\\"_strokeCircumference()\\\"\\n            [style.stroke-dashoffset.px]=\\\"_strokeDashOffset()\\\"\\n            [style.stroke-width.%]=\\\"_circleStrokeWidth()\\\"\\n            class=\\\"mdc-circular-progress__determinate-circle\\\"\\n            cx=\\\"50%\\\" cy=\\\"50%\\\"/>\\n  </svg>\\n</div>\\n<!--TODO: figure out why there are 3 separate svgs-->\\n<div class=\\\"mdc-circular-progress__indeterminate-container\\\" aria-hidden=\\\"true\\\">\\n  <div class=\\\"mdc-circular-progress__spinner-layer\\\">\\n    <div class=\\\"mdc-circular-progress__circle-clipper mdc-circular-progress__circle-left\\\">\\n      <ng-container [ngTemplateOutlet]=\\\"circle\\\"></ng-container>\\n    </div>\\n    <div class=\\\"mdc-circular-progress__gap-patch\\\">\\n      <ng-container [ngTemplateOutlet]=\\\"circle\\\"></ng-container>\\n    </div>\\n    <div class=\\\"mdc-circular-progress__circle-clipper mdc-circular-progress__circle-right\\\">\\n      <ng-container [ngTemplateOutlet]=\\\"circle\\\"></ng-container>\\n    </div>\\n  </div>\\n</div>\\n\",\n      styles: [\".mat-mdc-progress-spinner{display:block;overflow:hidden;line-height:0;position:relative;direction:ltr;transition:opacity 250ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-progress-spinner circle{stroke-width:var(--mat-progress-spinner-active-indicator-width, 4px)}.mat-mdc-progress-spinner._mat-animation-noopable,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__determinate-circle{transition:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__spinner-layer,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container{animation:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container circle{stroke-dasharray:0 !important}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle{stroke:currentColor;stroke:CanvasText}}.mdc-circular-progress__determinate-container,.mdc-circular-progress__indeterminate-circle-graphic,.mdc-circular-progress__indeterminate-container,.mdc-circular-progress__spinner-layer{position:absolute;width:100%;height:100%}.mdc-circular-progress__determinate-container{transform:rotate(-90deg)}.mdc-circular-progress--indeterminate .mdc-circular-progress__determinate-container{opacity:0}.mdc-circular-progress__indeterminate-container{font-size:0;letter-spacing:0;white-space:nowrap;opacity:0}.mdc-circular-progress--indeterminate .mdc-circular-progress__indeterminate-container{opacity:1;animation:mdc-circular-progress-container-rotate 1568.2352941176ms linear infinite}.mdc-circular-progress__determinate-circle-graphic,.mdc-circular-progress__indeterminate-circle-graphic{fill:rgba(0,0,0,0)}.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:var(--mat-progress-spinner-active-indicator-color, var(--mat-sys-primary))}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:CanvasText}}.mdc-circular-progress__determinate-circle{transition:stroke-dashoffset 500ms cubic-bezier(0, 0, 0.2, 1)}.mdc-circular-progress__gap-patch{position:absolute;top:0;left:47.5%;box-sizing:border-box;width:5%;height:100%;overflow:hidden}.mdc-circular-progress__gap-patch .mdc-circular-progress__indeterminate-circle-graphic{left:-900%;width:2000%;transform:rotate(180deg)}.mdc-circular-progress__circle-clipper .mdc-circular-progress__indeterminate-circle-graphic{width:200%}.mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{left:-100%}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-left .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-left-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-right-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress__circle-clipper{display:inline-flex;position:relative;width:50%;height:100%;overflow:hidden}.mdc-circular-progress--indeterminate .mdc-circular-progress__spinner-layer{animation:mdc-circular-progress-spinner-layer-rotate 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}@keyframes mdc-circular-progress-container-rotate{to{transform:rotate(360deg)}}@keyframes mdc-circular-progress-spinner-layer-rotate{12.5%{transform:rotate(135deg)}25%{transform:rotate(270deg)}37.5%{transform:rotate(405deg)}50%{transform:rotate(540deg)}62.5%{transform:rotate(675deg)}75%{transform:rotate(810deg)}87.5%{transform:rotate(945deg)}100%{transform:rotate(1080deg)}}@keyframes mdc-circular-progress-left-spin{from{transform:rotate(265deg)}50%{transform:rotate(130deg)}to{transform:rotate(265deg)}}@keyframes mdc-circular-progress-right-spin{from{transform:rotate(-265deg)}50%{transform:rotate(-130deg)}to{transform:rotate(-265deg)}}\\n\"]\n    }]\n  }], () => [], {\n    color: [{\n      type: Input\n    }],\n    _determinateCircle: [{\n      type: ViewChild,\n      args: ['determinateSpinner']\n    }],\n    mode: [{\n      type: Input\n    }],\n    value: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    diameter: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    strokeWidth: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }]\n  });\n})();\n/**\n * @deprecated Import Progress Spinner instead. Note that the\n *    `mat-spinner` selector isn't deprecated.\n * @breaking-change 16.0.0\n */\n// tslint:disable-next-line:variable-name\nconst MatSpinner = MatProgressSpinner;\nclass MatProgressSpinnerModule {\n  static ɵfac = function MatProgressSpinnerModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatProgressSpinnerModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatProgressSpinnerModule,\n    imports: [MatProgressSpinner, MatSpinner],\n    exports: [MatProgressSpinner, MatSpinner, MatCommonModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatProgressSpinnerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatProgressSpinner, MatSpinner],\n      exports: [MatProgressSpinner, MatSpinner, MatCommonModule]\n    }]\n  }], null, null);\n})();\nexport { MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS, MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY, MatProgressSpinner, MatProgressSpinnerModule, MatSpinner };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,IAAM,MAAM,CAAC,oBAAoB;AACjC,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe;AAClB,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,UAAU,GAAG,UAAU,EAAE;AAC5B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,WAAW,OAAO,SAAS,CAAC;AAC3C,IAAG,UAAU;AACb,IAAG,YAAY,oBAAoB,OAAO,qBAAqB,GAAG,IAAI,EAAE,qBAAqB,OAAO,qBAAqB,IAAI,GAAG,IAAI,EAAE,gBAAgB,OAAO,mBAAmB,GAAG,GAAG;AACtL,IAAG,YAAY,KAAK,OAAO,cAAc,CAAC;AAAA,EAC5C;AACF;AACA,IAAM,uCAAuC,IAAI,eAAe,wCAAwC;AAAA,EACtG,YAAY;AAAA,EACZ,SAAS;AACX,CAAC;AAMD,SAAS,+CAA+C;AACtD,SAAO;AAAA,IACL,UAAU;AAAA,EACZ;AACF;AAIA,IAAM,YAAY;AAIlB,IAAM,oBAAoB;AAC1B,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,cAAc,OAAO,UAAU;AAAA;AAAA,EAE/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,IAAI,QAAQ;AACV,WAAO,KAAK,UAAU,KAAK;AAAA,EAC7B;AAAA,EACA,IAAI,MAAM,OAAO;AACf,SAAK,SAAS;AAAA,EAChB;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA;AAAA,EAEhB;AAAA,EACA,cAAc;AACZ,UAAM,WAAW,OAAO,oCAAoC;AAC5D,SAAK,kBAAkB,oBAAoB,KAAK,CAAC,CAAC,YAAY,CAAC,SAAS;AACxE,SAAK,OAAO,KAAK,YAAY,cAAc,SAAS,YAAY,MAAM,gBAAgB,kBAAkB;AACxG,QAAI,UAAU;AACZ,UAAI,SAAS,OAAO;AAClB,aAAK,QAAQ,KAAK,gBAAgB,SAAS;AAAA,MAC7C;AACA,UAAI,SAAS,UAAU;AACrB,aAAK,WAAW,SAAS;AAAA,MAC3B;AACA,UAAI,SAAS,aAAa;AACxB,aAAK,cAAc,SAAS;AAAA,MAC9B;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA;AAAA;AAAA,EAEA,IAAI,QAAQ;AACV,WAAO,KAAK,SAAS,gBAAgB,KAAK,SAAS;AAAA,EACrD;AAAA,EACA,IAAI,MAAM,GAAG;AACX,SAAK,SAAS,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC,CAAC;AAAA,EACjD;AAAA,EACA,SAAS;AAAA;AAAA,EAET,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,MAAM;AACjB,SAAK,YAAY,QAAQ;AAAA,EAC3B;AAAA,EACA,YAAY;AAAA;AAAA,EAEZ,IAAI,cAAc;AAChB,WAAO,KAAK,gBAAgB,KAAK,WAAW;AAAA,EAC9C;AAAA,EACA,IAAI,YAAY,OAAO;AACrB,SAAK,eAAe,SAAS;AAAA,EAC/B;AAAA,EACA;AAAA;AAAA,EAEA,gBAAgB;AACd,YAAQ,KAAK,WAAW,qBAAqB;AAAA,EAC/C;AAAA;AAAA,EAEA,WAAW;AACT,UAAM,UAAU,KAAK,cAAc,IAAI,IAAI,KAAK;AAChD,WAAO,OAAO,OAAO,IAAI,OAAO;AAAA,EAClC;AAAA;AAAA,EAEA,uBAAuB;AACrB,WAAO,IAAI,KAAK,KAAK,KAAK,cAAc;AAAA,EAC1C;AAAA;AAAA,EAEA,oBAAoB;AAClB,QAAI,KAAK,SAAS,eAAe;AAC/B,aAAO,KAAK,qBAAqB,KAAK,MAAM,KAAK,UAAU;AAAA,IAC7D;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,qBAAqB;AACnB,WAAO,KAAK,cAAc,KAAK,WAAW;AAAA,EAC5C;AAAA,EACA,OAAO,OAAO,SAAS,2BAA2B,mBAAmB;AACnE,WAAO,KAAK,qBAAqB,qBAAoB;AAAA,EACvD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,sBAAsB,GAAG,CAAC,aAAa,CAAC;AAAA,IACrD,WAAW,SAAS,yBAAyB,IAAI,KAAK;AACpD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,qBAAqB,GAAG;AAAA,MAC3E;AAAA,IACF;AAAA,IACA,WAAW,CAAC,QAAQ,eAAe,YAAY,MAAM,GAAG,4BAA4B,uBAAuB;AAAA,IAC3G,UAAU;AAAA,IACV,cAAc,SAAS,gCAAgC,IAAI,KAAK;AAC9D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,iBAAiB,CAAC,EAAE,iBAAiB,GAAG,EAAE,iBAAiB,IAAI,SAAS,gBAAgB,IAAI,QAAQ,IAAI,EAAE,QAAQ,IAAI,IAAI;AACzI,QAAG,WAAW,SAAS,IAAI,KAAK;AAChC,QAAG,YAAY,SAAS,IAAI,UAAU,IAAI,EAAE,UAAU,IAAI,UAAU,IAAI,EAAE,+BAA+B,IAAI,WAAW,IAAI,EAAE,iDAAiD,IAAI,WAAW,IAAI;AAClM,QAAG,YAAY,2BAA2B,IAAI,eAAe,EAAE,wCAAwC,IAAI,SAAS,eAAe;AAAA,MACrI;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,MAAM;AAAA,MACN,OAAO,CAAC,GAAG,SAAS,SAAS,eAAe;AAAA,MAC5C,UAAU,CAAC,GAAG,YAAY,YAAY,eAAe;AAAA,MACrD,aAAa,CAAC,GAAG,eAAe,eAAe,eAAe;AAAA,IAChE;AAAA,IACA,UAAU,CAAC,oBAAoB;AAAA,IAC/B,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,UAAU,EAAE,GAAG,CAAC,sBAAsB,EAAE,GAAG,CAAC,eAAe,QAAQ,GAAG,8CAA8C,GAAG,CAAC,SAAS,8BAA8B,aAAa,SAAS,GAAG,mDAAmD,GAAG,CAAC,MAAM,OAAO,MAAM,OAAO,GAAG,2CAA2C,GAAG,CAAC,eAAe,QAAQ,GAAG,gDAAgD,GAAG,CAAC,GAAG,sCAAsC,GAAG,CAAC,GAAG,yCAAyC,oCAAoC,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,kCAAkC,GAAG,CAAC,GAAG,yCAAyC,qCAAqC,GAAG,CAAC,SAAS,8BAA8B,aAAa,SAAS,GAAG,qDAAqD,GAAG,CAAC,MAAM,OAAO,MAAM,KAAK,CAAC;AAAA,IAC7zB,UAAU,SAAS,4BAA4B,IAAI,KAAK;AACtD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,GAAG,2CAA2C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AACnH,QAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,QAAG,eAAe;AAClB,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,UAAU,GAAG,UAAU,CAAC;AAC3B,QAAG,aAAa,EAAE;AAClB,QAAG,gBAAgB;AACnB,QAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AACvD,QAAG,mBAAmB,GAAG,CAAC;AAC1B,QAAG,aAAa;AAChB,QAAG,eAAe,IAAI,OAAO,CAAC;AAC9B,QAAG,mBAAmB,IAAI,CAAC;AAC3B,QAAG,aAAa;AAChB,QAAG,eAAe,IAAI,OAAO,EAAE;AAC/B,QAAG,mBAAmB,IAAI,CAAC;AAC3B,QAAG,aAAa,EAAE,EAAE;AAAA,MACtB;AACA,UAAI,KAAK,GAAG;AACV,cAAM,YAAe,YAAY,CAAC;AAClC,QAAG,UAAU,CAAC;AACd,QAAG,YAAY,WAAW,IAAI,SAAS,CAAC;AACxC,QAAG,UAAU;AACb,QAAG,YAAY,oBAAoB,IAAI,qBAAqB,GAAG,IAAI,EAAE,qBAAqB,IAAI,kBAAkB,GAAG,IAAI,EAAE,gBAAgB,IAAI,mBAAmB,GAAG,GAAG;AACtK,QAAG,YAAY,KAAK,IAAI,cAAc,CAAC;AACvC,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,oBAAoB,SAAS;AAC3C,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,oBAAoB,SAAS;AAC3C,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,oBAAoB,SAAS;AAAA,MAC7C;AAAA,IACF;AAAA,IACA,cAAc,CAAC,gBAAgB;AAAA,IAC/B,QAAQ,CAAC,irIAAirI;AAAA,IAC1rI,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,QAAQ;AAAA,QACR,SAAS;AAAA;AAAA;AAAA,QAGT,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,mCAAmC;AAAA,QACnC,gDAAgD;AAAA,QAChD,oBAAoB;AAAA,QACpB,qBAAqB;AAAA,QACrB,uCAAuC;AAAA,QACvC,yDAAyD;AAAA,QACzD,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,eAAe;AAAA,MACjB;AAAA,MACA,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,SAAS,CAAC,gBAAgB;AAAA,MAC1B,UAAU;AAAA,MACV,QAAQ,CAAC,irIAAirI;AAAA,IAC5rI,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,IAAM,aAAa;AACnB,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,OAAO,OAAO,SAAS,iCAAiC,mBAAmB;AACzE,WAAO,KAAK,qBAAqB,2BAA0B;AAAA,EAC7D;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,oBAAoB,UAAU;AAAA,IACxC,SAAS,CAAC,oBAAoB,YAAY,eAAe;AAAA,EAC3D,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,eAAe;AAAA,EAC3B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,oBAAoB,UAAU;AAAA,MACxC,SAAS,CAAC,oBAAoB,YAAY,eAAe;AAAA,IAC3D,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}