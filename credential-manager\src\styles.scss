/* Global Styles for Credential Manager */

// Global styles
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Roboto', 'Helvetica Neue', sans-serif;
  background-color: #f5f5f5;
}

body {
  margin: 0;
  font-family: <PERSON><PERSON>, "Helvetica Neue", sans-serif;
}

// Material Icons styling
.material-icons {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

// Fix for mat-icon
mat-icon {
  font-family: 'Material Icons' !important;
}

// Custom utility classes
.full-width {
  width: 100% !important;
}

.text-center {
  text-align: center !important;
}

.mt-16 {
  margin-top: 16px !important;
}

.mb-16 {
  margin-bottom: 16px !important;
}

.p-16 {
  padding: 16px !important;
}

.p-24 {
  padding: 24px !important;
}

// Loading spinner styles
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

// Error message styles
.error-message {
  color: #f44336;
  font-size: 14px;
  margin-top: 8px;
}

// Success message styles
.success-message {
  color: #4caf50;
  font-size: 14px;
  margin-top: 8px;
}

// Snackbar styles
.success-snackbar {
  background-color: #4caf50 !important;
  color: white !important;
}

.error-snackbar {
  background-color: #f44336 !important;
  color: white !important;
}

.warning-snackbar {
  background-color: #ff9800 !important;
  color: white !important;
}

// Card hover effects
.mat-mdc-card {
  transition: box-shadow 0.3s ease-in-out, transform 0.2s ease-in-out;

  &:hover {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }
}

// Button improvements
.mat-mdc-raised-button {
  font-weight: 500;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
}

// Form field improvements
.mat-mdc-form-field {
  .mat-mdc-form-field-error {
    font-size: 12px;
    margin-top: 4px;
  }
}

// Responsive design
@media (max-width: 768px) {
  .mat-mdc-card {
    margin: 8px;
  }

  .container {
    padding: 16px;
  }
}

// Animation classes
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}
