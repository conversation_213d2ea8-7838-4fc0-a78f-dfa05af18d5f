import { Component, inject, signal } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatDialogRef, MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { CommonModule } from '@angular/common';

import { CredentialService } from '../../services/credential.service';
import { EncryptionService } from '../../services/encryption.service';


@Component({
  selector: 'app-add-credential',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule
  ],
  templateUrl: './add-credential.html',
  styleUrl: './add-credential.scss'
})
export class AddCredential {
  private fb = inject(FormBuilder);
  private credentialService = inject(CredentialService);
  private encryptionService = inject(EncryptionService);
  private dialogRef = inject(MatDialogRef<AddCredential>);
  private snackBar = inject(MatSnackBar);

  credentialForm: FormGroup;
  credentialType = signal<'password' | 'api-key'>('password');
  isLoading = signal(false);
  hidePassword = signal(true);

  usageTypes = ['production', 'development', 'testing', 'personal', 'other'];

  constructor() {
    this.credentialForm = this.fb.group({
      title: ['', Validators.required],
      type: ['password', Validators.required],

      // Password fields
      username: [''],
      email: [''],
      password: [''],
      website: [''],
      notes: [''],

      // API Key fields
      provider: [''],
      apiKey: [''],
      keyName: [''],
      description: [''],
      usage: [''],
      endpoint: ['']
    });

    // Watch for type changes
    this.credentialForm.get('type')?.valueChanges.subscribe(type => {
      this.credentialType.set(type);
      this.updateValidators();
    });
  }

  private updateValidators() {
    const type = this.credentialType();

    // Clear all validators first
    Object.keys(this.credentialForm.controls).forEach(key => {
      if (key !== 'title' && key !== 'type') {
        this.credentialForm.get(key)?.clearValidators();
      }
    });

    if (type === 'password') {
      this.credentialForm.get('password')?.setValidators([Validators.required]);
    } else if (type === 'api-key') {
      this.credentialForm.get('provider')?.setValidators([Validators.required]);
      this.credentialForm.get('apiKey')?.setValidators([Validators.required]);
    }

    // Update validity
    Object.keys(this.credentialForm.controls).forEach(key => {
      this.credentialForm.get(key)?.updateValueAndValidity();
    });
  }

  generatePassword() {
    const generatedPassword = this.encryptionService.generateSecurePassword(16);
    this.credentialForm.patchValue({ password: generatedPassword });
  }

  checkPasswordStrength() {
    const password = this.credentialForm.get('password')?.value;
    if (password) {
      const strength = this.encryptionService.checkPasswordStrength(password);
      console.log('Password strength:', strength);
      // You can show this in UI
    }
  }

  async onSubmit() {
    if (this.credentialForm.valid && !this.isLoading()) {
      this.isLoading.set(true);

      try {
        const formValue = this.credentialForm.value;
        const type = this.credentialType();

        if (type === 'password') {
          const passwordCredential = {
            title: formValue.title,
            type: 'password' as const,
            username: formValue.username,
            email: formValue.email,
            password: formValue.password,
            website: formValue.website,
            notes: formValue.notes
          };
          await this.credentialService.addCredential(passwordCredential);
        } else {
          const apiKeyCredential = {
            title: formValue.title,
            type: 'api-key' as const,
            provider: formValue.provider,
            apiKey: formValue.apiKey,
            keyName: formValue.keyName,
            description: formValue.description,
            usage: formValue.usage,
            endpoint: formValue.endpoint
          };
          await this.credentialService.addCredential(apiKeyCredential);
        }

        this.snackBar.open('Credential added successfully!', 'Close', { duration: 3000 });
        this.dialogRef.close(true);
      } catch (error: any) {
        this.snackBar.open(error.message || 'Failed to add credential', 'Close', { duration: 5000 });
      } finally {
        this.isLoading.set(false);
      }
    }
  }

  onCancel() {
    this.dialogRef.close(false);
  }

  togglePasswordVisibility() {
    this.hidePassword.set(!this.hidePassword());
  }
}
