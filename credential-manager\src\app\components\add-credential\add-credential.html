<div class="add-credential-dialog">
  <h2 mat-dialog-title>Add New Credential</h2>

  <mat-dialog-content>
    <form [formGroup]="credentialForm" class="credential-form">
      <!-- Basic Info -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Title</mat-label>
        <input matInput formControlName="title" placeholder="Enter a title for this credential">
        <mat-error *ngIf="credentialForm.get('title')?.hasError('required')">
          Title is required
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Type</mat-label>
        <mat-select formControlName="type">
          <mat-option value="password">Password</mat-option>
          <mat-option value="api-key">API Key</mat-option>
        </mat-select>
      </mat-form-field>

      <!-- Password Fields -->
      <div *ngIf="credentialType() === 'password'" class="password-fields">
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Website/Service</mat-label>
          <input matInput formControlName="website" placeholder="e.g., google.com">
        </mat-form-field>

        <div class="form-row">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Username</mat-label>
            <input matInput formControlName="username" placeholder="Username">
          </mat-form-field>

          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Email</mat-label>
            <input matInput type="email" formControlName="email" placeholder="Email">
          </mat-form-field>
        </div>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Password</mat-label>
          <input matInput [type]="hidePassword() ? 'password' : 'text'"
                 formControlName="password" placeholder="Enter password">
          <button mat-icon-button matSuffix type="button"
                  (click)="togglePasswordVisibility()">
            <mat-icon>{{hidePassword() ? 'visibility_off' : 'visibility'}}</mat-icon>
          </button>
          <mat-error *ngIf="credentialForm.get('password')?.hasError('required')">
            Password is required
          </mat-error>
        </mat-form-field>

        <div class="password-actions">
          <button mat-button type="button" color="accent" (click)="generatePassword()">
            <mat-icon>auto_fix_high</mat-icon>
            Generate Strong Password
          </button>
        </div>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Notes</mat-label>
          <textarea matInput formControlName="notes" rows="3"
                    placeholder="Additional notes (optional)"></textarea>
        </mat-form-field>
      </div>

      <!-- API Key Fields -->
      <div *ngIf="credentialType() === 'api-key'" class="api-key-fields">
        <div class="form-row">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Provider</mat-label>
            <input matInput formControlName="provider"
                   placeholder="e.g., OpenAI, Google, AWS, Custom Provider">
            <mat-error *ngIf="credentialForm.get('provider')?.hasError('required')">
              Provider is required
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Key Name</mat-label>
            <input matInput formControlName="keyName"
                   placeholder="e.g., Production Key, Dev Key">
          </mat-form-field>
        </div>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>API Key</mat-label>
          <input matInput [type]="hidePassword() ? 'password' : 'text'"
                 formControlName="apiKey" placeholder="Enter your API key">
          <button mat-icon-button matSuffix type="button"
                  (click)="togglePasswordVisibility()">
            <mat-icon>{{hidePassword() ? 'visibility_off' : 'visibility'}}</mat-icon>
          </button>
          <mat-error *ngIf="credentialForm.get('apiKey')?.hasError('required')">
            API Key is required
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Usage</mat-label>
          <mat-select formControlName="usage">
            <mat-option *ngFor="let usage of usageTypes" [value]="usage">
              {{usage | titlecase}}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>API Endpoint (Optional)</mat-label>
          <input matInput formControlName="endpoint"
                 placeholder="Custom API endpoint URL">
        </mat-form-field>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Description</mat-label>
          <textarea matInput formControlName="description" rows="3"
                    placeholder="Description or notes about this API key"></textarea>
        </mat-form-field>
      </div>
    </form>
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <button mat-button (click)="onCancel()">Cancel</button>
    <button mat-raised-button color="primary"
            (click)="onSubmit()"
            [disabled]="!credentialForm.valid || isLoading()">
      <span *ngIf="!isLoading()">Add Credential</span>
      <span *ngIf="isLoading()">Adding...</span>
    </button>
  </mat-dialog-actions>
</div>
