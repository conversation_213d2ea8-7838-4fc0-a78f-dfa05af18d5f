import { Injectable, inject } from '@angular/core';
import { Firestore, collection, addDoc, updateDoc, deleteDoc, doc, getDocs, query, where, orderBy, onSnapshot } from '@angular/fire/firestore';
import { Observable, BehaviorSubject } from 'rxjs';
import { CredentialType, PasswordCredential, ApiKeyCredential } from '../models/credential.model';
import { AuthService } from './auth.service';
import { EncryptionService } from './encryption.service';
import { ValidationService } from './validation.service';

@Injectable({
  providedIn: 'root'
})
export class CredentialService {
  private firestore = inject(Firestore);
  private authService = inject(AuthService);
  private encryptionService = inject(EncryptionService);
  
  private credentialsSubject = new BehaviorSubject<CredentialType[]>([]);
  public credentials$ = this.credentialsSubject.asObservable();

  constructor() {
    console.log('CredentialService constructor called');
    this.authService.user$.subscribe(user => {
      console.log('Auth state changed in CredentialService:', user);
      if (user) {
        console.log('Loading credentials for user:', user.uid);
        console.log('User email:', user.email);
        this.loadUserCredentials(user.uid);
      } else {
        console.log('No user authenticated, clearing credentials');
        this.credentialsSubject.next([]);
      }
    });
  }

  private loadUserCredentials(userId: string): void {
    try {
      console.log('Loading credentials for userId:', userId);
      const credentialsRef = collection(this.firestore, 'credentials');

      // First, let's try to get all documents to see if there are any access issues
      console.log('Attempting to fetch all credentials to test access...');
      getDocs(credentialsRef).then(snapshot => {
        console.log('All credentials count:', snapshot.size);
        snapshot.forEach(doc => {
          const data = doc.data();
          console.log('Document ID:', doc.id, 'UserId in doc:', data['userId'], 'Current userId:', userId);
        });
      }).catch(error => {
        console.error('Error fetching all credentials:', error);
      });

      const q = query(
        credentialsRef,
        where('userId', '==', userId),
        orderBy('updatedAt', 'desc')
      );
      console.log('Firestore query created for userId:', userId);

      onSnapshot(q, (snapshot) => {
        try {
          console.log('Firebase snapshot received, documents count:', snapshot.size);
          const credentials: CredentialType[] = [];
          snapshot.forEach((doc) => {
            try {
              const data = doc.data();
              console.log('Processing document:', doc.id, 'Data:', data);

              // Validate required fields
              if (!data || !data['type'] || !data['title'] || !data['userId']) {
                console.warn(`Invalid credential data for document ${doc.id}`, data);
                return;
              }

              const credential = {
                id: doc.id,
                ...data,
                createdAt: data['createdAt']?.toDate() || new Date(),
                updatedAt: data['updatedAt']?.toDate() || new Date()
              } as CredentialType;

              // Decrypt sensitive data with error handling
              try {
                if (credential.type === 'password') {
                  const passwordCred = credential as PasswordCredential;
                  if (passwordCred.password) {
                    passwordCred.password = this.encryptionService.decrypt(passwordCred.password);
                  }
                } else if (credential.type === 'api-key') {
                  const apiKeyCred = credential as ApiKeyCredential;
                  if (apiKeyCred.apiKey) {
                    apiKeyCred.apiKey = this.encryptionService.decrypt(apiKeyCred.apiKey);
                  }
                }
              } catch (decryptError) {
                console.error(`Failed to decrypt credential ${doc.id}:`, decryptError);
                // Skip this credential if decryption fails
                return;
              }

              credentials.push(credential);
              console.log('Successfully processed credential:', credential.id, credential.type, credential.title);
            } catch (docError) {
              console.error(`Error processing credential document ${doc.id}:`, docError);
            }
          });
          console.log('Final credentials array:', credentials);
          this.credentialsSubject.next(credentials);
        } catch (snapshotError) {
          console.error('Error processing credentials snapshot:', snapshotError);
          // Don't clear existing credentials on error
        }
      }, (error) => {
        console.error('Error loading credentials:', error);
        const errorMessage = ValidationService.getErrorMessage(error);
        throw new Error(`Failed to load credentials: ${errorMessage}`);
      });
    } catch (error) {
      console.error('Error setting up credentials listener:', error);
      throw new Error('Failed to initialize credentials loading');
    }
  }

  async addCredential(credential: Omit<CredentialType, 'id' | 'userId' | 'createdAt' | 'updatedAt'>): Promise<void> {
    try {
      // Validate user authentication
      const user = this.authService.getCurrentUser();
      if (!user) {
        throw new Error('User not authenticated. Please sign in to continue.');
      }

      // Validate credential data
      this.validateCredentialData(credential);

      // Check rate limiting
      const rateLimitKey = `add_credential_${user.uid}`;
      if (!ValidationService.checkRateLimit(rateLimitKey, 10, 60000)) { // 10 requests per minute
        throw new Error('Too many credential additions. Please wait before adding more.');
      }

      const now = new Date();
      let credentialData: any = {
        ...credential,
        userId: user.uid,
        createdAt: now,
        updatedAt: now,
        // Sanitize text fields
        title: ValidationService.sanitizeInput(credential.title)
      };

      // Type-specific data handling
      if (credential.type === 'password') {
        const passwordCred = credential as PasswordCredential;
        credentialData.notes = ValidationService.sanitizeInput(passwordCred.notes || '');
      } else if (credential.type === 'api-key') {
        const apiKeyCred = credential as ApiKeyCredential;
        credentialData.description = ValidationService.sanitizeInput(apiKeyCred.description || '');
      }

      // Encrypt sensitive data with validation
      try {
        if (credential.type === 'password') {
          const passwordCred = credential as PasswordCredential;
          if (!passwordCred.password) {
            throw new Error('Password is required for password credentials');
          }
          credentialData.password = this.encryptionService.encrypt(passwordCred.password);
        } else if (credential.type === 'api-key') {
          const apiKeyCred = credential as ApiKeyCredential;
          if (!apiKeyCred.apiKey) {
            throw new Error('API key is required for API key credentials');
          }
          credentialData.apiKey = this.encryptionService.encrypt(apiKeyCred.apiKey);
        }
      } catch (encryptError) {
        console.error('Encryption error:', encryptError);
        throw new Error('Failed to secure credential data. Please try again.');
      }

      const credentialsRef = collection(this.firestore, 'credentials');
      await addDoc(credentialsRef, credentialData);
    } catch (error) {
      console.error('Error adding credential:', error);
      const errorMessage = ValidationService.getErrorMessage(error);
      throw new Error(`Failed to add credential: ${errorMessage}`);
    }
  }

  async updateCredential(id: string, updates: Partial<CredentialType>): Promise<void> {
    try {
      // Validate user authentication
      const user = this.authService.getCurrentUser();
      if (!user) {
        throw new Error('User not authenticated. Please sign in to continue.');
      }

      // Validate credential ID
      if (!id || id.trim().length === 0) {
        throw new Error('Credential ID is required');
      }

      // Check if credential exists and belongs to user
      const existingCredential = this.getCredentialById(id);
      if (!existingCredential) {
        throw new Error('Credential not found');
      }

      if (existingCredential.userId !== user.uid) {
        throw new Error('Access denied. You can only update your own credentials.');
      }

      // Check rate limiting
      const rateLimitKey = `update_credential_${user.uid}`;
      if (!ValidationService.checkRateLimit(rateLimitKey, 20, 60000)) { // 20 requests per minute
        throw new Error('Too many updates. Please wait before making more changes.');
      }

      // Validate update data
      if (updates.title !== undefined) {
        if (!updates.title || updates.title.trim().length === 0) {
          throw new Error('Credential title cannot be empty');
        }
        if (updates.title.length > 100) {
          throw new Error('Credential title is too long (maximum 100 characters)');
        }
      }

      const updateData: any = {
        ...updates,
        updatedAt: new Date()
      };

      // Sanitize text fields
      if (updates.title) {
        updateData.title = ValidationService.sanitizeInput(updates.title);
      }

      // Encrypt sensitive data if being updated
      try {
        if (updates.type === 'password' && 'password' in updates && updates.password) {
          updateData.password = this.encryptionService.encrypt(updates.password);
        } else if (updates.type === 'api-key' && 'apiKey' in updates && updates.apiKey) {
          // Validate API key format
          const apiKeyValidation = ValidationService.apiKeyValidator()(
            { value: updates.apiKey } as any
          );
          if (apiKeyValidation) {
            throw new Error('Invalid API key format');
          }
          updateData.apiKey = this.encryptionService.encrypt(updates.apiKey);
        }
      } catch (encryptError) {
        console.error('Encryption error:', encryptError);
        throw new Error('Failed to secure updated credential data. Please try again.');
      }

      const credentialRef = doc(this.firestore, 'credentials', id);
      await updateDoc(credentialRef, updateData);
    } catch (error) {
      console.error('Error updating credential:', error);
      const errorMessage = ValidationService.getErrorMessage(error);
      throw new Error(`Failed to update credential: ${errorMessage}`);
    }
  }

  async deleteCredential(id: string): Promise<void> {
    try {
      // Validate user authentication
      const user = this.authService.getCurrentUser();
      if (!user) {
        throw new Error('User not authenticated. Please sign in to continue.');
      }

      // Validate credential ID
      if (!id || id.trim().length === 0) {
        throw new Error('Credential ID is required');
      }

      // Check if credential exists and belongs to user
      const existingCredential = this.getCredentialById(id);
      if (!existingCredential) {
        throw new Error('Credential not found');
      }

      if (existingCredential.userId !== user.uid) {
        throw new Error('Access denied. You can only delete your own credentials.');
      }

      // Check rate limiting
      const rateLimitKey = `delete_credential_${user.uid}`;
      if (!ValidationService.checkRateLimit(rateLimitKey, 10, 60000)) { // 10 requests per minute
        throw new Error('Too many deletions. Please wait before deleting more credentials.');
      }

      const credentialRef = doc(this.firestore, 'credentials', id);
      await deleteDoc(credentialRef);
    } catch (error) {
      console.error('Error deleting credential:', error);
      const errorMessage = ValidationService.getErrorMessage(error);
      throw new Error(`Failed to delete credential: ${errorMessage}`);
    }
  }

  getCredentials(): CredentialType[] {
    return this.credentialsSubject.value;
  }

  getCredentialById(id: string): CredentialType | undefined {
    return this.credentialsSubject.value.find(cred => cred.id === id);
  }

  getPasswordCredentials(): PasswordCredential[] {
    return this.credentialsSubject.value.filter(cred => cred.type === 'password') as PasswordCredential[];
  }

  getApiKeyCredentials(): ApiKeyCredential[] {
    return this.credentialsSubject.value.filter(cred => cred.type === 'api-key') as ApiKeyCredential[];
  }

  getApiKeysByProvider(provider: string): ApiKeyCredential[] {
    return this.getApiKeyCredentials().filter(cred =>
      cred.provider.toLowerCase() === provider.toLowerCase()
    );
  }

  getValidApiKeys(): ApiKeyCredential[] {
    return this.getApiKeyCredentials().filter(cred => cred.isValid === true);
  }



  getApiKeysByUsage(usage: string): ApiKeyCredential[] {
    return this.getApiKeyCredentials().filter(cred => cred.usage === usage);
  }

  private validateCredentialData(credential: Omit<CredentialType, 'id' | 'userId' | 'createdAt' | 'updatedAt'>): void {
    // Basic validation
    if (!credential.title || credential.title.trim().length === 0) {
      throw new Error('Credential title is required');
    }

    if (credential.title.length > 100) {
      throw new Error('Credential title is too long (maximum 100 characters)');
    }

    if (!credential.type || !['password', 'api-key'].includes(credential.type)) {
      throw new Error('Invalid credential type');
    }

    // Type-specific validation
    if (credential.type === 'password') {
      const passwordCred = credential as PasswordCredential;

      if (!passwordCred.password) {
        throw new Error('Password is required');
      }

      if (passwordCred.password.length > 256) {
        throw new Error('Password is too long');
      }

      if (passwordCred.email && passwordCred.email.length > 0) {
        const emailValidation = ValidationService.emailValidator()(
          { value: passwordCred.email } as any
        );
        if (emailValidation) {
          throw new Error('Invalid email address');
        }
      }

      if (passwordCred.website && passwordCred.website.length > 0) {
        const urlValidation = ValidationService.urlValidator()(
          { value: passwordCred.website } as any
        );
        if (urlValidation) {
          throw new Error('Invalid website URL');
        }
      }

      if (passwordCred.notes && passwordCred.notes.length > 1000) {
        throw new Error('Notes are too long (maximum 1000 characters)');
      }
    } else if (credential.type === 'api-key') {
      const apiKeyCred = credential as ApiKeyCredential;

      if (!apiKeyCred.apiKey) {
        throw new Error('API key is required');
      }

      if (!apiKeyCred.provider || apiKeyCred.provider.trim().length === 0) {
        throw new Error('API provider is required');
      }

      if (apiKeyCred.provider.length > 50) {
        throw new Error('Provider name is too long (maximum 50 characters)');
      }

      const apiKeyValidation = ValidationService.apiKeyValidator()(
        { value: apiKeyCred.apiKey } as any
      );
      if (apiKeyValidation) {
        throw new Error('Invalid API key format');
      }

      if (apiKeyCred.endpoint && apiKeyCred.endpoint.length > 0) {
        const urlValidation = ValidationService.urlValidator()(
          { value: apiKeyCred.endpoint } as any
        );
        if (urlValidation) {
          throw new Error('Invalid endpoint URL');
        }
      }

      if (apiKeyCred.description && apiKeyCred.description.length > 500) {
        throw new Error('Description is too long (maximum 500 characters)');
      }

      if (apiKeyCred.keyName && apiKeyCred.keyName.length > 100) {
        throw new Error('Key name is too long (maximum 100 characters)');
      }

      if (apiKeyCred.monthlyLimit && (apiKeyCred.monthlyLimit < 0 || apiKeyCred.monthlyLimit > 1000000)) {
        throw new Error('Monthly limit must be between 0 and 1,000,000');
      }

      if (apiKeyCred.currentUsage && apiKeyCred.currentUsage < 0) {
        throw new Error('Current usage cannot be negative');
      }
    }
  }
}
