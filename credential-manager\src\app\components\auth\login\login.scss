.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #3f51b5 0%, #2196f3 50%, #00bcd4 100%);
  padding: 20px;
  position: relative;
  overflow: hidden;

  // Animated background elements
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: float 20s infinite linear;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(1px);
  }
}

@keyframes float {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.login-card {
  width: 100%;
  max-width: 420px;
  padding: 32px;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  position: relative;
  z-index: 1;
  animation: slideUp 0.6s ease-out;

  mat-card-header {
    text-align: center;
    margin-bottom: 24px;

    mat-card-title {
      font-size: 28px;
      font-weight: 600;
      color: #3f51b5;
      margin-bottom: 8px;
    }

    mat-card-subtitle {
      font-size: 16px;
      color: #666;
      font-weight: 400;
    }
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.full-width {
  width: 100%;
  margin-bottom: 20px;

  .mat-mdc-form-field {
    .mat-mdc-text-field-wrapper {
      border-radius: 8px;
    }
  }
}

.login-button {
  height: 52px;
  font-size: 16px;
  font-weight: 600;
  margin-top: 24px;
  border-radius: 8px;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;

  &:not(:disabled):hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(63, 81, 181, 0.3);
  }

  &:disabled {
    opacity: 0.7;
  }
}

mat-card-actions {
  text-align: center;
  padding-top: 24px;
  border-top: 1px solid #e0e0e0;
  margin-top: 24px;

  p {
    margin: 12px 0;
    font-size: 14px;
    color: #666;

    a {
      color: #3f51b5;
      text-decoration: none;
      font-weight: 500;
      transition: color 0.3s ease;

      &:hover {
        color: #2196f3;
        text-decoration: underline;
      }
    }
  }
}

// Loading state
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 16px;
  z-index: 10;
}

// Error states
.mat-mdc-form-field.mat-form-field-invalid {
  .mat-mdc-text-field-wrapper {
    border-color: #f44336;
  }
}

// Responsive design
@media (max-width: 480px) {
  .login-container {
    padding: 16px;
  }

  .login-card {
    padding: 24px;
    max-width: 100%;
  }
}