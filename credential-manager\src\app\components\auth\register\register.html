<div class="register-container">
  <mat-card class="register-card">
    <!-- Loading overlay -->
    <div class="loading-overlay" *ngIf="isLoading()">
      <mat-spinner diameter="40"></mat-spinner>
    </div>

    <mat-card-header>
      <mat-card-title>
        <mat-icon style="vertical-align: middle; margin-right: 8px; color: #2196f3;">person_add</mat-icon>
        Create Account
      </mat-card-title>
      <mat-card-subtitle>Join Credential Manager and secure your digital life</mat-card-subtitle>
    </mat-card-header>

    <mat-card-content>
      <form [formGroup]="registerForm" (ngSubmit)="onSubmit()" novalidate>
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Email Address</mat-label>
          <input
            matInput
            type="email"
            formControlName="email"
            placeholder="Enter your email address"
            autocomplete="email"
            [class.mat-form-field-invalid]="registerForm.get('email')?.invalid && registerForm.get('email')?.touched">
          <mat-icon matSuffix color="primary">email</mat-icon>
          <mat-error *ngIf="registerForm.get('email')?.hasError('required') && registerForm.get('email')?.touched">
            <mat-icon style="vertical-align: middle; margin-right: 4px; font-size: 16px;">error</mat-icon>
            Email address is required
          </mat-error>
          <mat-error *ngIf="registerForm.get('email')?.hasError('email') && registerForm.get('email')?.touched">
            <mat-icon style="vertical-align: middle; margin-right: 4px; font-size: 16px;">error</mat-icon>
            Please enter a valid email address
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Password</mat-label>
          <input
            matInput
            [type]="hidePassword() ? 'password' : 'text'"
            formControlName="password"
            placeholder="Create a strong password"
            autocomplete="new-password"
            (input)="onPasswordChange()"
            [class.mat-form-field-invalid]="registerForm.get('password')?.invalid && registerForm.get('password')?.touched">
          <button
            mat-icon-button
            matSuffix
            type="button"
            (click)="togglePasswordVisibility()"
            [attr.aria-label]="hidePassword() ? 'Show password' : 'Hide password'"
            [attr.aria-pressed]="!hidePassword()">
            <mat-icon>{{hidePassword() ? 'visibility_off' : 'visibility'}}</mat-icon>
          </button>
          <mat-error *ngIf="registerForm.get('password')?.hasError('required') && registerForm.get('password')?.touched">
            <mat-icon style="vertical-align: middle; margin-right: 4px; font-size: 16px;">error</mat-icon>
            Password is required
          </mat-error>
          <mat-error *ngIf="registerForm.get('password')?.hasError('minlength') && registerForm.get('password')?.touched">
            <mat-icon style="vertical-align: middle; margin-right: 4px; font-size: 16px;">error</mat-icon>
            Password must be at least 6 characters long
          </mat-error>
        </mat-form-field>

        <!-- Password strength indicator -->
        <div class="password-strength" *ngIf="registerForm.get('password')?.value">
          <div class="strength-bar">
            <div class="strength-fill" [class]="passwordStrength()"></div>
          </div>
          <div class="strength-text">
            Password strength: <strong>{{ getPasswordStrengthText() }}</strong>
          </div>
        </div>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Confirm Password</mat-label>
          <input
            matInput
            [type]="hideConfirmPassword() ? 'password' : 'text'"
            formControlName="confirmPassword"
            placeholder="Confirm your password"
            autocomplete="new-password"
            [class.mat-form-field-invalid]="(registerForm.get('confirmPassword')?.invalid || registerForm.hasError('passwordMismatch')) && registerForm.get('confirmPassword')?.touched">
          <button
            mat-icon-button
            matSuffix
            type="button"
            (click)="toggleConfirmPasswordVisibility()"
            [attr.aria-label]="hideConfirmPassword() ? 'Show password' : 'Hide password'"
            [attr.aria-pressed]="!hideConfirmPassword()">
            <mat-icon>{{hideConfirmPassword() ? 'visibility_off' : 'visibility'}}</mat-icon>
          </button>
          <mat-error *ngIf="registerForm.get('confirmPassword')?.hasError('required') && registerForm.get('confirmPassword')?.touched">
            <mat-icon style="vertical-align: middle; margin-right: 4px; font-size: 16px;">error</mat-icon>
            Please confirm your password
          </mat-error>
          <mat-error *ngIf="registerForm.hasError('passwordMismatch') && registerForm.get('confirmPassword')?.touched">
            <mat-icon style="vertical-align: middle; margin-right: 4px; font-size: 16px;">error</mat-icon>
            Passwords do not match
          </mat-error>
        </mat-form-field>

        <!-- Terms and conditions -->
        <div style="margin-bottom: 16px;">
          <mat-checkbox formControlName="acceptTerms" color="primary" required>
            I agree to the <a href="#" style="color: #2196f3;">Terms of Service</a> and
            <a href="#" style="color: #2196f3;">Privacy Policy</a>
          </mat-checkbox>
          <mat-error *ngIf="registerForm.get('acceptTerms')?.hasError('required') && registerForm.get('acceptTerms')?.touched"
                     style="display: block; margin-top: 8px; font-size: 12px; color: #f44336;">
            <mat-icon style="vertical-align: middle; margin-right: 4px; font-size: 16px;">error</mat-icon>
            You must accept the terms and conditions
          </mat-error>
        </div>

        <button
          mat-raised-button
          color="primary"
          type="submit"
          class="full-width register-button"
          [disabled]="!registerForm.valid || isLoading()">
          <mat-icon *ngIf="!isLoading()" style="margin-right: 8px;">person_add</mat-icon>
          <mat-spinner *ngIf="isLoading()" diameter="20" style="margin-right: 8px;"></mat-spinner>
          <span *ngIf="!isLoading()">Create Account</span>
          <span *ngIf="isLoading()">Creating Account...</span>
        </button>

        <!-- Additional info -->
        <div style="margin-top: 16px; text-align: center;">
          <mat-divider style="margin: 16px 0;"></mat-divider>
          <p style="color: #666; font-size: 14px; margin: 16px 0;">
            Your data is encrypted and secure with Firebase Authentication
          </p>
        </div>
      </form>
    </mat-card-content>

    <mat-card-actions>
      <div style="text-align: center; width: 100%;">
        <p>
          <mat-icon style="vertical-align: middle; margin-right: 4px; font-size: 16px; color: #666;">login</mat-icon>
          Already have an account?
          <a routerLink="/login" style="font-weight: 500;">Sign In</a>
        </p>
      </div>
    </mat-card-actions>
  </mat-card>
</div>
